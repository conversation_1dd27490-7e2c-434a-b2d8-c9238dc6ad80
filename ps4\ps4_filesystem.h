// Copyright 2025 <Copyright Owner>

#pragma once

#include <cstdint>
#include <filesystem>
#include <functional>
#include <istream>
#include <mutex>
#include <ostream>
#include <shared_mutex>
#include <string>
#include <sys/stat.h>
#include <sys/types.h>
#include <unordered_map>
#include <vector>

// Windows compatibility definitions
#if defined(_MSC_VER) || defined(_WIN32) || defined(_WIN64)
#include <BaseTsd.h>
typedef SSIZE_T ssize_t;
typedef unsigned int mode_t;
#endif

// Undefine conflicting Windows macros
#ifdef CreateDirectoryA
#undef CreateDirectoryA
#endif
#ifdef CreateDirectoryW
#undef CreateDirectoryW
#endif
#ifdef CreateDirectory
#undef CreateDirectory
#endif
#ifdef RemoveDirectoryA
#undef RemoveDirectoryA
#endif
#ifdef RemoveDirectoryW
#undef RemoveDirectoryW
#endif
#ifdef RemoveDirectory
#undef RemoveDirectory
#endif
#ifdef EncryptFileA
#undef EncryptFileA
#endif
#ifdef EncryptFileW
#undef EncryptFileW
#endif
#ifdef EncryptFile
#undef EncryptFile
#endif
#ifdef DecryptFileA
#undef DecryptFileA
#endif
#ifdef DecryptFileW
#undef DecryptFileW
#endif
#ifdef DecryptFile
#undef DecryptFile
#endif

namespace ps4 {

class PS4Emulator;

// Filesystem exception
struct FilesystemException : std::runtime_error {
  explicit FilesystemException(const std::string &msg) : std::runtime_error(msg) {}
};

// PS4-specific file types
enum class PS4FileType {
  Regular, Directory, Device, Special, PFS, SaveData, Trophy, System
};

// File entry structure
struct FileEntry {
  std::string path;              // Virtual file path
  std::string hostPath;          // Mapped host path
  uint64_t size = 0;             // File size in bytes
  int protection = 0;            // Memory protection flags
  std::vector<uint8_t> data;     // In-memory file data
  uint32_t mode = 0;             // File permissions
  uint64_t creationTime = 0;     // Creation timestamp
  uint64_t modificationTime = 0; // Modification timestamp
  uint64_t accessTime = 0;       // Last access timestamp
  bool isDir = false;            // Directory flag
  uint64_t cacheHits = 0;        // Cache hits
  uint64_t cacheMisses = 0;      // Cache misses
  PS4FileType fileType = PS4FileType::Regular; // PS4 file type
  uint32_t ps4Permissions = 0;                 // PS4-specific permissions
  std::string mountPoint;                      // Mount point
  bool isEncrypted = false;                    // Encryption flag
  std::vector<uint8_t> encryptionKey;          // Encryption key
  uint64_t blockSize = 4096;                   // Block size for PFS
  std::vector<uint8_t> checksum;               // File integrity checksum
  bool present = true;                         // File presence flag
};

// File handle structure
struct FileHandle {
  std::string path;         // Virtual file path
  uint32_t flags = 0;       // Open flags
  uint64_t offset = 0;      // Current file offset
  int hostFd = -1;          // Host OS file descriptor
  int fd = -1;              // Emulator file descriptor
  uint64_t cacheHits = 0;   // Cache hits
  uint64_t cacheMisses = 0; // Cache misses
};

// Filesystem statistics
struct FilesystemStats {
  uint64_t operationCount = 0;  // Total operations
  uint64_t totalLatencyUs = 0;  // Total latency (microseconds)
  uint64_t cacheHits = 0;       // Cache hits
  uint64_t cacheMisses = 0;     // Cache misses
  uint64_t fileAccessCount = 0; // Total file accesses
};

// Mount points management (inspired by shadPS4's MntPoints)
class MntPoints {
public:
  struct MntPair {
    std::filesystem::path host_path;
    std::string mount; // e.g., /app0
    bool read_only;
  };

  void Mount(const std::filesystem::path &host_folder, const std::string &guest_folder,
             bool read_only = false);
  void Unmount(const std::string &guest_folder);
  void UnmountAll();
  std::filesystem::path GetHostPath(const std::string &guest_path, bool *is_read_only = nullptr);
  using IterateDirectoryCallback = std::function<void(const std::filesystem::path &, bool)>;
  void IterateDirectory(const std::string &guest_directory, const IterateDirectoryCallback &callback);

private:
  std::vector<MntPair> m_mount_pairs;
  std::mutex m_mutex;
  std::vector<std::filesystem::path> path_parts;
  std::unordered_map<std::string, std::filesystem::path> path_cache;
};

// File descriptor management (inspired by shadPS4's HandleTable)
class HandleTable {
public:
  struct File {
    std::atomic_bool is_opened{false};
    std::atomic<PS4FileType> type{PS4FileType::Regular};
    std::filesystem::path host_name;
    std::string guest_name;
    int host_fd = -1; // Host file descriptor
    std::mutex mutex;
  };

  int CreateHandle();
  void DeleteHandle(int fd);
  File *GetFile(int fd);
  File *GetFile(const std::filesystem::path &host_name);
  void CreateStdHandles();

private:
  std::vector<File *> m_files;
  std::mutex m_mutex;
};

// Enhanced filesystem settings
struct EnhancedSettings {
  std::string defaultMountPoint = "/app0"; // Default mount point
  int defaultFileMode = 0644;              // Default file permissions
  int defaultDirMode = 0755;               // Default directory permissions
  bool enableCaseSensitivity = false;      // Case sensitivity flag
  bool enableEncryption = false;           // Encryption flag
  uint64_t pfsBlockSize = 4096;            // PFS block size
  std::vector<std::string> additionalMounts; // Additional mount points
  std::string saveDataPath = "/savedata";    // Save data mount point
  std::string trophyPath = "/trophy";        // Trophy data mount point
  std::string systemPath = "/system";        // System mount point
  bool enableDeviceFiles = true;             // Device file emulation
  bool enablePFS = true;                     // PFS enable flag
  bool enablePermissionChecks = false;       // Permission checks flag
};

// PS4 virtual filesystem manager
class PS4Filesystem {
public:
  explicit PS4Filesystem(PS4Emulator &emu);
  PS4Filesystem();
  ~PS4Filesystem();

  bool Initialize();
  void Shutdown();

  int OpenFile(const std::string &path, int flags, mode_t mode);
  int CloseFile(int fd);
  ssize_t ReadFile(int fd, void *buf, size_t count);
  bool ReadFile(const std::string &path, std::vector<uint8_t> &data);
  std::vector<std::string> ListFiles(const std::string &path, bool recursive = false);
  ssize_t WriteFile(int fd, const void *buf, size_t count);
  bool WriteFile(const std::string &path, const void *data, size_t size);
  off_t SeekFile(int fd, off_t offset, int whence);
#ifdef _WIN32
  int StatFile(const std::string &path, struct _stat64i32 *buf);
#else
  int StatFile(const std::string &path, struct stat *buf);
#endif
  bool CreateDirectory(const std::string &path, mode_t mode);
  bool CreateDirectoryW(const std::wstring &path);
  bool CreateVirtualDirectory(const std::string &path);
  bool RemoveDirectory(const std::string &path);
  bool MountDirectory(const std::wstring &path);
  uint64_t AllocateVirtualMemory(uint64_t size, uint64_t alignment, bool shared);
  bool FreeVirtualMemory(uint64_t address);
  bool ProtectMemory(uint64_t address, uint64_t size, int protection);
  uint64_t SceKernelGetProcessId();
  std::string DumpState() const;
  void SaveState(std::ostream &out) const;
  void LoadState(std::istream &in);
  FilesystemStats GetStats() const;
  void SetSettings(const EnhancedSettings &settings);
  const EnhancedSettings &GetSettings() const;
  bool SaveSettings(const std::string &filename) const;
  bool LoadSettings(const std::string &filename);
  bool LoadGame(const std::string &gamePath);
  bool StartGame();
  std::string GetLoadedGamePath() const;
  bool IsGameLoaded() const;
  std::string GetGameDirectory() const;
  void SetGameDirectory(const std::string &path);

  // PS4-specific methods
  void InitializeDeviceFiles();
  bool CreateDeviceFile(const std::string &path, PS4FileType deviceType);
  bool HandleDeviceAccess(const std::string &path, void *buffer, size_t size, bool isWrite);
  PS4FileType DetermineFileType(const std::string &path) const;
  bool ValidatePS4Permissions(const std::string &path, int mode);
  std::string MapToHostPath(const std::string &virtualPath);
  bool InitializePFS();
  bool EncryptFile(const std::string &path, const std::vector<uint8_t> &key);
  bool DecryptFile(const std::string &path);

private:
  PS4Emulator &m_emulator;                // Emulator reference
  mutable std::shared_mutex m_mutex;      // Thread-safety mutex
  std::string m_rootPath;                 // Virtual filesystem root
  MntPoints m_mountPoints;                // Mount points
  HandleTable m_handleTable;              // File descriptor table
  std::unordered_map<std::string, FileEntry> m_files; // Virtual file entries
  std::unordered_map<int, FileHandle> m_fileHandles;  // Open file handles
  std::vector<std::string> m_directories;             // Virtual directories
  std::unordered_map<std::string, PS4FileType> m_deviceFiles; // Device files
  int m_nextFd;                         // Next file descriptor
  mutable FilesystemStats m_stats;      // Filesystem statistics
  EnhancedSettings m_settings;          // Configuration settings
  std::string m_loadedGamePath;         // Loaded game path
  std::string m_gameDirectory;          // Game directory path
  bool m_gameLoaded = false;            // Game loaded flag
  std::vector<uint8_t> m_encryptionKey; // Filesystem encryption key

  std::string ResolvePath(const std::string &virtualPath) const;
  std::vector<uint8_t> CalculateChecksum(const std::vector<uint8_t> &data) const;
  bool CreateDeviceFileInternal(const std::string &path, PS4FileType deviceType);
  bool InitializePFSInternal();
};

} // namespace ps4

---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:6 (project)"
    message: |
      The system is: Windows - 10.0.26120 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:6 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      MSBuild version 17.13.19+0d9f5a35a for .NET Framework
      Build started 6/15/2025 2:57:41 AM.
      
      Project "D:\\sss\\src\\build\\CMakeFiles\\3.30.5-msvc23\\CompilerIdC\\CompilerIdC.vcxproj" on node 1 (default targets).
      PrepareForBuild:
        Creating directory "Debug\\".
        Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
        Creating directory "Debug\\CompilerIdC.tlog\\".
      InitializeBuildStatus:
        Creating "Debug\\CompilerIdC.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
        Touching "Debug\\CompilerIdC.tlog\\unsuccessfulbuild".
      VcpkgTripletSelection:
        Using triplet "x64-windows" from "D:\\vcpkg\\installed\\x64-windows\\"
        Using normalized configuration "Release"
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /I"D:\\vcpkg\\installed\\x64-windows\\include" /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TC /FC /errorReport:queue CMakeCCompilerId.c
        CMakeCCompilerId.c
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdC.exe" /INCREMENTAL:NO /NOLOGO /LIBPATH:"D:\\vcpkg\\installed\\x64-windows\\lib" /LIBPATH:"D:\\vcpkg\\installed\\x64-windows\\lib\\manual-link" kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib "D:\\vcpkg\\installed\\x64-windows\\lib\\*.lib" /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdC.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdC.lib" /MACHINE:X64 Debug\\CMakeCCompilerId.obj
        CompilerIdC.vcxproj -> D:\\sss\\src\\build\\CMakeFiles\\3.30.5-msvc23\\CompilerIdC\\CompilerIdC.exe
      AppLocalFromInstalled:
        pwsh.exe -ExecutionPolicy Bypass -noprofile -File "D:\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "D:\\sss\\src\\build\\CMakeFiles\\3.30.5-msvc23\\CompilerIdC\\CompilerIdC.exe" "D:\\vcpkg\\installed\\x64-windows\\bin" "Debug\\CompilerIdC.tlog\\CompilerIdC.write.1u.tlog" "Debug\\vcpkg.applocal.log"
        'pwsh.exe' is not recognized as an internal or external command,
        operable program or batch file.
        The command "pwsh.exe -ExecutionPolicy Bypass -noprofile -File "D:\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "D:\\sss\\src\\build\\CMakeFiles\\3.30.5-msvc23\\CompilerIdC\\CompilerIdC.exe" "D:\\vcpkg\\installed\\x64-windows\\bin" "Debug\\CompilerIdC.tlog\\CompilerIdC.write.1u.tlog" "Debug\\vcpkg.applocal.log"" exited with code 9009.
        "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\powershell.exe" -ExecutionPolicy Bypass -noprofile -File "D:\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "D:\\sss\\src\\build\\CMakeFiles\\3.30.5-msvc23\\CompilerIdC\\CompilerIdC.exe" "D:\\vcpkg\\installed\\x64-windows\\bin" "Debug\\CompilerIdC.tlog\\CompilerIdC.write.1u.tlog" "Debug\\vcpkg.applocal.log"
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_C_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_C_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        Deleting file "Debug\\CompilerIdC.tlog\\unsuccessfulbuild".
        Touching "Debug\\CompilerIdC.tlog\\CompilerIdC.lastbuildstate".
      Done Building Project "D:\\sss\\src\\build\\CMakeFiles\\3.30.5-msvc23\\CompilerIdC\\CompilerIdC.vcxproj" (default targets).
      
      Build succeeded.
          0 Warning(s)
          0 Error(s)
      
      Time Elapsed 00:00:04.36
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.exe"
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.vcxproj"
      
      The C compiler identification is MSVC, found in:
        D:/sss/src/build/CMakeFiles/3.30.5-msvc23/CompilerIdC/CompilerIdC.exe
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:6 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      MSBuild version 17.13.19+0d9f5a35a for .NET Framework
      Build started 6/15/2025 2:57:45 AM.
      
      Project "D:\\sss\\src\\build\\CMakeFiles\\3.30.5-msvc23\\CompilerIdCXX\\CompilerIdCXX.vcxproj" on node 1 (default targets).
      PrepareForBuild:
        Creating directory "Debug\\".
        Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
        Creating directory "Debug\\CompilerIdCXX.tlog\\".
      InitializeBuildStatus:
        Creating "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
        Touching "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
      VcpkgTripletSelection:
        Using triplet "x64-windows" from "D:\\vcpkg\\installed\\x64-windows\\"
        Using normalized configuration "Release"
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /I"D:\\vcpkg\\installed\\x64-windows\\include" /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TP /FC /errorReport:queue CMakeCXXCompilerId.cpp
        CMakeCXXCompilerId.cpp
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO /LIBPATH:"D:\\vcpkg\\installed\\x64-windows\\lib" /LIBPATH:"D:\\vcpkg\\installed\\x64-windows\\lib\\manual-link" kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib "D:\\vcpkg\\installed\\x64-windows\\lib\\*.lib" /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCXX.lib" /MACHINE:X64 Debug\\CMakeCXXCompilerId.obj
        CompilerIdCXX.vcxproj -> D:\\sss\\src\\build\\CMakeFiles\\3.30.5-msvc23\\CompilerIdCXX\\CompilerIdCXX.exe
      AppLocalFromInstalled:
        pwsh.exe -ExecutionPolicy Bypass -noprofile -File "D:\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "D:\\sss\\src\\build\\CMakeFiles\\3.30.5-msvc23\\CompilerIdCXX\\CompilerIdCXX.exe" "D:\\vcpkg\\installed\\x64-windows\\bin" "Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.write.1u.tlog" "Debug\\vcpkg.applocal.log"
        'pwsh.exe' is not recognized as an internal or external command,
        operable program or batch file.
        The command "pwsh.exe -ExecutionPolicy Bypass -noprofile -File "D:\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "D:\\sss\\src\\build\\CMakeFiles\\3.30.5-msvc23\\CompilerIdCXX\\CompilerIdCXX.exe" "D:\\vcpkg\\installed\\x64-windows\\bin" "Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.write.1u.tlog" "Debug\\vcpkg.applocal.log"" exited with code 9009.
        "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\powershell.exe" -ExecutionPolicy Bypass -noprofile -File "D:\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "D:\\sss\\src\\build\\CMakeFiles\\3.30.5-msvc23\\CompilerIdCXX\\CompilerIdCXX.exe" "D:\\vcpkg\\installed\\x64-windows\\bin" "Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.write.1u.tlog" "Debug\\vcpkg.applocal.log"
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_CXX_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_CXX_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        Deleting file "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
        Touching "Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.lastbuildstate".
      Done Building Project "D:\\sss\\src\\build\\CMakeFiles\\3.30.5-msvc23\\CompilerIdCXX\\CompilerIdCXX.vcxproj" (default targets).
      
      Build succeeded.
          0 Warning(s)
          0 Error(s)
      
      Time Elapsed 00:00:01.18
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.exe"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.vcxproj"
      
      The CXX compiler identification is MSVC, found in:
        D:/sss/src/build/CMakeFiles/3.30.5-msvc23/CompilerIdCXX/CompilerIdCXX.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:6 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "D:/sss/src/build/CMakeFiles/CMakeScratch/TryCompile-nj0347"
      binary: "D:/sss/src/build/CMakeFiles/CMakeScratch/TryCompile-nj0347"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      VCPKG_INSTALLED_DIR: "D:/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "D:/vcpkg"
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'D:/sss/src/build/CMakeFiles/CMakeScratch/TryCompile-nj0347'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_43dfb.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.13.19+0d9f5a35a for .NET Framework
        Build started 6/15/2025 2:57:47 AM.
        
        Project "D:\\sss\\src\\build\\CMakeFiles\\CMakeScratch\\TryCompile-nj0347\\cmTC_43dfb.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_43dfb.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "D:\\sss\\src\\build\\CMakeFiles\\CMakeScratch\\TryCompile-nj0347\\Debug\\".
          Creating directory "cmTC_43dfb.dir\\Debug\\cmTC_43dfb.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_43dfb.dir\\Debug\\cmTC_43dfb.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_43dfb.dir\\Debug\\cmTC_43dfb.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_43dfb.dir\\Debug\\\\" /Fd"cmTC_43dfb.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\share\\cmake-3.30\\Modules\\CMakeCCompilerABI.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.43.34810 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_43dfb.dir\\Debug\\\\" /Fd"cmTC_43dfb.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\share\\cmake-3.30\\Modules\\CMakeCCompilerABI.c"
          CMakeCCompilerABI.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"D:\\sss\\src\\build\\CMakeFiles\\CMakeScratch\\TryCompile-nj0347\\Debug\\cmTC_43dfb.exe" /INCREMENTAL /ILK:"cmTC_43dfb.dir\\Debug\\cmTC_43dfb.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/sss/src/build/CMakeFiles/CMakeScratch/TryCompile-nj0347/Debug/cmTC_43dfb.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/sss/src/build/CMakeFiles/CMakeScratch/TryCompile-nj0347/Debug/cmTC_43dfb.lib" /MACHINE:X64  /machine:x64 cmTC_43dfb.dir\\Debug\\CMakeCCompilerABI.obj
          cmTC_43dfb.vcxproj -> D:\\sss\\src\\build\\CMakeFiles\\CMakeScratch\\TryCompile-nj0347\\Debug\\cmTC_43dfb.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_43dfb.dir\\Debug\\cmTC_43dfb.tlog\\unsuccessfulbuild".
          Touching "cmTC_43dfb.dir\\Debug\\cmTC_43dfb.tlog\\cmTC_43dfb.lastbuildstate".
        Done Building Project "D:\\sss\\src\\build\\CMakeFiles\\CMakeScratch\\TryCompile-nj0347\\cmTC_43dfb.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.54
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:6 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'C': C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:6 (project)"
    message: |
      Running the C compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.43.34810.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:6 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "D:/sss/src/build/CMakeFiles/CMakeScratch/TryCompile-h4s12o"
      binary: "D:/sss/src/build/CMakeFiles/CMakeScratch/TryCompile-h4s12o"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      VCPKG_INSTALLED_DIR: "D:/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "D:/vcpkg"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'D:/sss/src/build/CMakeFiles/CMakeScratch/TryCompile-h4s12o'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_d1b95.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.13.19+0d9f5a35a for .NET Framework
        Build started 6/15/2025 2:57:48 AM.
        
        Project "D:\\sss\\src\\build\\CMakeFiles\\CMakeScratch\\TryCompile-h4s12o\\cmTC_d1b95.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_d1b95.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "D:\\sss\\src\\build\\CMakeFiles\\CMakeScratch\\TryCompile-h4s12o\\Debug\\".
          Creating directory "cmTC_d1b95.dir\\Debug\\cmTC_d1b95.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_d1b95.dir\\Debug\\cmTC_d1b95.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_d1b95.dir\\Debug\\cmTC_d1b95.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_d1b95.dir\\Debug\\\\" /Fd"cmTC_d1b95.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\share\\cmake-3.30\\Modules\\CMakeCXXCompilerABI.cpp"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.43.34810 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_d1b95.dir\\Debug\\\\" /Fd"cmTC_d1b95.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\share\\cmake-3.30\\Modules\\CMakeCXXCompilerABI.cpp"
          CMakeCXXCompilerABI.cpp
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"D:\\sss\\src\\build\\CMakeFiles\\CMakeScratch\\TryCompile-h4s12o\\Debug\\cmTC_d1b95.exe" /INCREMENTAL /ILK:"cmTC_d1b95.dir\\Debug\\cmTC_d1b95.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/sss/src/build/CMakeFiles/CMakeScratch/TryCompile-h4s12o/Debug/cmTC_d1b95.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/sss/src/build/CMakeFiles/CMakeScratch/TryCompile-h4s12o/Debug/cmTC_d1b95.lib" /MACHINE:X64  /machine:x64 cmTC_d1b95.dir\\Debug\\CMakeCXXCompilerABI.obj
          cmTC_d1b95.vcxproj -> D:\\sss\\src\\build\\CMakeFiles\\CMakeScratch\\TryCompile-h4s12o\\Debug\\cmTC_d1b95.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_d1b95.dir\\Debug\\cmTC_d1b95.tlog\\unsuccessfulbuild".
          Touching "cmTC_d1b95.dir\\Debug\\cmTC_d1b95.tlog\\cmTC_d1b95.lastbuildstate".
        Done Building Project "D:\\sss\\src\\build\\CMakeFiles\\CMakeScratch\\TryCompile-h4s12o\\cmTC_d1b95.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.53
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:6 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'CXX': C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:6 (project)"
    message: |
      Running the CXX compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.43.34810.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Internal/CheckSourceCompiles.cmake:101 (try_compile)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CheckCSourceCompiles.cmake:52 (cmake_check_source_compiles)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindThreads.cmake:97 (CHECK_C_SOURCE_COMPILES)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindThreads.cmake:163 (_threads_check_libc)"
      - "D:/vcpkg/scripts/buildsystems/vcpkg.cmake:893 (_find_package)"
      - "D:/vcpkg/installed/x64-windows/share/spdlog/spdlogConfig.cmake:30 (find_package)"
      - "D:/vcpkg/scripts/buildsystems/vcpkg.cmake:893 (_find_package)"
      - "CMakeLists.txt:83 (find_package)"
    checks:
      - "Performing Test CMAKE_HAVE_LIBC_PTHREAD"
    directories:
      source: "D:/sss/src/build/CMakeFiles/CMakeScratch/TryCompile-demxoo"
      binary: "D:/sss/src/build/CMakeFiles/CMakeScratch/TryCompile-demxoo"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/vcpkg/installed/x64-windows/share/ffmpeg"
      VCPKG_INSTALLED_DIR: "D:/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "D:/vcpkg"
    buildResult:
      variable: "CMAKE_HAVE_LIBC_PTHREAD"
      cached: true
      stdout: |
        Change Dir: 'D:/sss/src/build/CMakeFiles/CMakeScratch/TryCompile-demxoo'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_535de.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.13.19+0d9f5a35a for .NET Framework
        Build started 6/15/2025 2:57:49 AM.
        
        Project "D:\\sss\\src\\build\\CMakeFiles\\CMakeScratch\\TryCompile-demxoo\\cmTC_535de.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_535de.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "D:\\sss\\src\\build\\CMakeFiles\\CMakeScratch\\TryCompile-demxoo\\Debug\\".
          Creating directory "cmTC_535de.dir\\Debug\\cmTC_535de.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_535de.dir\\Debug\\cmTC_535de.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_535de.dir\\Debug\\cmTC_535de.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CMAKE_HAVE_LIBC_PTHREAD /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_535de.dir\\Debug\\\\" /Fd"cmTC_535de.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\sss\\src\\build\\CMakeFiles\\CMakeScratch\\TryCompile-demxoo\\src.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.43.34810 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CMAKE_HAVE_LIBC_PTHREAD /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_535de.dir\\Debug\\\\" /Fd"cmTC_535de.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\sss\\src\\build\\CMakeFiles\\CMakeScratch\\TryCompile-demxoo\\src.c"
          src.c
        D:\\sss\\src\\build\\CMakeFiles\\CMakeScratch\\TryCompile-demxoo\\src.c(1,10): error C1083: Cannot open include file: 'pthread.h': No such file or directory [D:\\sss\\src\\build\\CMakeFiles\\CMakeScratch\\TryCompile-demxoo\\cmTC_535de.vcxproj]
        Done Building Project "D:\\sss\\src\\build\\CMakeFiles\\CMakeScratch\\TryCompile-demxoo\\cmTC_535de.vcxproj" (default targets) -- FAILED.
        
        Build FAILED.
        
        "D:\\sss\\src\\build\\CMakeFiles\\CMakeScratch\\TryCompile-demxoo\\cmTC_535de.vcxproj" (default target) (1) ->
        (ClCompile target) -> 
          D:\\sss\\src\\build\\CMakeFiles\\CMakeScratch\\TryCompile-demxoo\\src.c(1,10): error C1083: Cannot open include file: 'pthread.h': No such file or directory [D:\\sss\\src\\build\\CMakeFiles\\CMakeScratch\\TryCompile-demxoo\\cmTC_535de.vcxproj]
        
            0 Warning(s)
            1 Error(s)
        
        Time Elapsed 00:00:00.44
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CheckLibraryExists.cmake:69 (try_compile)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindThreads.cmake:112 (CHECK_LIBRARY_EXISTS)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindThreads.cmake:175 (_threads_check_lib)"
      - "D:/vcpkg/scripts/buildsystems/vcpkg.cmake:893 (_find_package)"
      - "D:/vcpkg/installed/x64-windows/share/spdlog/spdlogConfig.cmake:30 (find_package)"
      - "D:/vcpkg/scripts/buildsystems/vcpkg.cmake:893 (_find_package)"
      - "CMakeLists.txt:83 (find_package)"
    checks:
      - "Looking for pthread_create in pthreads"
    directories:
      source: "D:/sss/src/build/CMakeFiles/CMakeScratch/TryCompile-7tdhnd"
      binary: "D:/sss/src/build/CMakeFiles/CMakeScratch/TryCompile-7tdhnd"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/vcpkg/installed/x64-windows/share/ffmpeg"
      VCPKG_INSTALLED_DIR: "D:/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "D:/vcpkg"
    buildResult:
      variable: "CMAKE_HAVE_PTHREADS_CREATE"
      cached: true
      stdout: |
        Change Dir: 'D:/sss/src/build/CMakeFiles/CMakeScratch/TryCompile-7tdhnd'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_a3e53.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.13.19+0d9f5a35a for .NET Framework
        Build started 6/15/2025 2:57:50 AM.
        
        Project "D:\\sss\\src\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7tdhnd\\cmTC_a3e53.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_a3e53.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "D:\\sss\\src\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7tdhnd\\Debug\\".
          Creating directory "cmTC_a3e53.dir\\Debug\\cmTC_a3e53.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_a3e53.dir\\Debug\\cmTC_a3e53.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_a3e53.dir\\Debug\\cmTC_a3e53.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_a3e53.dir\\Debug\\\\" /Fd"cmTC_a3e53.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\sss\\src\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7tdhnd\\CheckFunctionExists.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.43.34810 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_a3e53.dir\\Debug\\\\" /Fd"cmTC_a3e53.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\sss\\src\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7tdhnd\\CheckFunctionExists.c"
          CheckFunctionExists.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"D:\\sss\\src\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7tdhnd\\Debug\\cmTC_a3e53.exe" /INCREMENTAL /ILK:"cmTC_a3e53.dir\\Debug\\cmTC_a3e53.ilk" /NOLOGO pthreads.lib kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/sss/src/build/CMakeFiles/CMakeScratch/TryCompile-7tdhnd/Debug/cmTC_a3e53.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/sss/src/build/CMakeFiles/CMakeScratch/TryCompile-7tdhnd/Debug/cmTC_a3e53.lib" /MACHINE:X64  /machine:x64 cmTC_a3e53.dir\\Debug\\CheckFunctionExists.obj
        LINK : fatal error LNK1104: cannot open file 'pthreads.lib' [D:\\sss\\src\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7tdhnd\\cmTC_a3e53.vcxproj]
        Done Building Project "D:\\sss\\src\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7tdhnd\\cmTC_a3e53.vcxproj" (default targets) -- FAILED.
        
        Build FAILED.
        
        "D:\\sss\\src\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7tdhnd\\cmTC_a3e53.vcxproj" (default target) (1) ->
        (Link target) -> 
          LINK : fatal error LNK1104: cannot open file 'pthreads.lib' [D:\\sss\\src\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7tdhnd\\cmTC_a3e53.vcxproj]
        
            0 Warning(s)
            1 Error(s)
        
        Time Elapsed 00:00:00.43
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CheckLibraryExists.cmake:69 (try_compile)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindThreads.cmake:112 (CHECK_LIBRARY_EXISTS)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindThreads.cmake:176 (_threads_check_lib)"
      - "D:/vcpkg/scripts/buildsystems/vcpkg.cmake:893 (_find_package)"
      - "D:/vcpkg/installed/x64-windows/share/spdlog/spdlogConfig.cmake:30 (find_package)"
      - "D:/vcpkg/scripts/buildsystems/vcpkg.cmake:893 (_find_package)"
      - "CMakeLists.txt:83 (find_package)"
    checks:
      - "Looking for pthread_create in pthread"
    directories:
      source: "D:/sss/src/build/CMakeFiles/CMakeScratch/TryCompile-uv4ggm"
      binary: "D:/sss/src/build/CMakeFiles/CMakeScratch/TryCompile-uv4ggm"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/vcpkg/installed/x64-windows/share/ffmpeg"
      VCPKG_INSTALLED_DIR: "D:/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "D:/vcpkg"
    buildResult:
      variable: "CMAKE_HAVE_PTHREAD_CREATE"
      cached: true
      stdout: |
        Change Dir: 'D:/sss/src/build/CMakeFiles/CMakeScratch/TryCompile-uv4ggm'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_089de.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.13.19+0d9f5a35a for .NET Framework
        Build started 6/15/2025 2:57:50 AM.
        
        Project "D:\\sss\\src\\build\\CMakeFiles\\CMakeScratch\\TryCompile-uv4ggm\\cmTC_089de.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_089de.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "D:\\sss\\src\\build\\CMakeFiles\\CMakeScratch\\TryCompile-uv4ggm\\Debug\\".
          Creating directory "cmTC_089de.dir\\Debug\\cmTC_089de.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_089de.dir\\Debug\\cmTC_089de.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_089de.dir\\Debug\\cmTC_089de.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_089de.dir\\Debug\\\\" /Fd"cmTC_089de.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\sss\\src\\build\\CMakeFiles\\CMakeScratch\\TryCompile-uv4ggm\\CheckFunctionExists.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.43.34810 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_089de.dir\\Debug\\\\" /Fd"cmTC_089de.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\sss\\src\\build\\CMakeFiles\\CMakeScratch\\TryCompile-uv4ggm\\CheckFunctionExists.c"
          CheckFunctionExists.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"D:\\sss\\src\\build\\CMakeFiles\\CMakeScratch\\TryCompile-uv4ggm\\Debug\\cmTC_089de.exe" /INCREMENTAL /ILK:"cmTC_089de.dir\\Debug\\cmTC_089de.ilk" /NOLOGO pthread.lib kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/sss/src/build/CMakeFiles/CMakeScratch/TryCompile-uv4ggm/Debug/cmTC_089de.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/sss/src/build/CMakeFiles/CMakeScratch/TryCompile-uv4ggm/Debug/cmTC_089de.lib" /MACHINE:X64  /machine:x64 cmTC_089de.dir\\Debug\\CheckFunctionExists.obj
        LINK : fatal error LNK1104: cannot open file 'pthread.lib' [D:\\sss\\src\\build\\CMakeFiles\\CMakeScratch\\TryCompile-uv4ggm\\cmTC_089de.vcxproj]
        Done Building Project "D:\\sss\\src\\build\\CMakeFiles\\CMakeScratch\\TryCompile-uv4ggm\\cmTC_089de.vcxproj" (default targets) -- FAILED.
        
        Build FAILED.
        
        "D:\\sss\\src\\build\\CMakeFiles\\CMakeScratch\\TryCompile-uv4ggm\\cmTC_089de.vcxproj" (default target) (1) ->
        (Link target) -> 
          LINK : fatal error LNK1104: cannot open file 'pthread.lib' [D:\\sss\\src\\build\\CMakeFiles\\CMakeScratch\\TryCompile-uv4ggm\\cmTC_089de.vcxproj]
        
            0 Warning(s)
            1 Error(s)
        
        Time Elapsed 00:00:00.43
        
      exitCode: 1
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CheckSymbolExists.cmake:154 (try_compile)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CheckSymbolExists.cmake:66 (__CHECK_SYMBOL_EXISTS_IMPL)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindBZip2.cmake:96 (CHECK_SYMBOL_EXISTS)"
      - "D:/vcpkg/scripts/buildsystems/vcpkg.cmake:893 (_find_package)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeFindDependencyMacro.cmake:76 (find_package)"
      - "D:/vcpkg/installed/x64-windows/share/libzip/libzip-config.cmake:42 (find_dependency)"
      - "D:/vcpkg/scripts/buildsystems/vcpkg.cmake:893 (_find_package)"
      - "CMakeLists.txt:82 (find_package)"
    checks:
      - "Looking for BZ2_bzCompressInit"
    directories:
      source: "D:/sss/src/build/CMakeFiles/CMakeScratch/TryCompile-0844vm"
      binary: "D:/sss/src/build/CMakeFiles/CMakeScratch/TryCompile-0844vm"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/vcpkg/installed/x64-windows/share/ffmpeg;D:/vcpkg/installed/x64-windows/share/libzip/modules"
      VCPKG_INSTALLED_DIR: "D:/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "D:/vcpkg"
    buildResult:
      variable: "BZIP2_NEED_PREFIX"
      cached: true
      stdout: |
        Change Dir: 'D:/sss/src/build/CMakeFiles/CMakeScratch/TryCompile-0844vm'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_e4709.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.13.19+0d9f5a35a for .NET Framework
        Build started 6/15/2025 3:22:50 AM.
        
        Project "D:\\sss\\src\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0844vm\\cmTC_e4709.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_e4709.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "D:\\sss\\src\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0844vm\\Debug\\".
          Creating directory "cmTC_e4709.dir\\Debug\\cmTC_e4709.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_e4709.dir\\Debug\\cmTC_e4709.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_e4709.dir\\Debug\\cmTC_e4709.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /I"D:\\vcpkg\\installed\\x64-windows\\include" /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_e4709.dir\\Debug\\\\" /Fd"cmTC_e4709.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\sss\\src\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0844vm\\CheckSymbolExists.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.43.34810 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /I"D:\\vcpkg\\installed\\x64-windows\\include" /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_e4709.dir\\Debug\\\\" /Fd"cmTC_e4709.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\sss\\src\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0844vm\\CheckSymbolExists.c"
          CheckSymbolExists.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"D:\\sss\\src\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0844vm\\Debug\\cmTC_e4709.exe" /INCREMENTAL /ILK:"cmTC_e4709.dir\\Debug\\cmTC_e4709.ilk" /NOLOGO "D:\\vcpkg\\installed\\x64-windows\\debug\\lib\\bz2d.lib" kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/sss/src/build/CMakeFiles/CMakeScratch/TryCompile-0844vm/Debug/cmTC_e4709.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/sss/src/build/CMakeFiles/CMakeScratch/TryCompile-0844vm/Debug/cmTC_e4709.lib" /MACHINE:X64  /machine:x64 cmTC_e4709.dir\\Debug\\CheckSymbolExists.obj
          cmTC_e4709.vcxproj -> D:\\sss\\src\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0844vm\\Debug\\cmTC_e4709.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_e4709.dir\\Debug\\cmTC_e4709.tlog\\unsuccessfulbuild".
          Touching "cmTC_e4709.dir\\Debug\\cmTC_e4709.tlog\\cmTC_e4709.lastbuildstate".
        Done Building Project "D:\\sss\\src\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0844vm\\cmTC_e4709.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.59
        
      exitCode: 0
...

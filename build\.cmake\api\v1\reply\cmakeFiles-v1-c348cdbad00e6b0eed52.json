{"inputs": [{"path": "CMakeLists.txt"}, {"isGenerated": true, "path": "build/CMakeFiles/3.30.5-msvc23/CMakeSystem.cmake"}, {"isExternal": true, "path": "D:/vcpkg/scripts/buildsystems/vcpkg.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeDependentOption.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeSystemSpecificInitialize.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Platform/Windows-Initialize.cmake"}, {"isGenerated": true, "path": "build/CMakeFiles/3.30.5-msvc23/CMakeCCompiler.cmake"}, {"isGenerated": true, "path": "build/CMakeFiles/3.30.5-msvc23/CMakeCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeSystemSpecificInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeGenericSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeInitializeConfigs.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Platform/Windows.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Platform/WindowsPaths.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/MSVC-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/MSVC.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Platform/Windows-MSVC-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Platform/Windows-MSVC.cmake"}, {"isGenerated": true, "path": "build/CMakeFiles/3.30.5-msvc23/CMakeRCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeRCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeCXXInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/MSVC-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/MSVC.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Platform/Windows-MSVC-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Platform/Windows-MSVC.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeCommonLanguageInclude.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/sdl2/SDL2ConfigVersion.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/sdl2/SDL2Config.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FeatureSummary.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/sdl2/SDL2Targets.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/sdl2/SDL2Targets-debug.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/sdl2/SDL2Targets-release.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/sdl2/SDL2mainTargets.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/sdl2/SDL2mainTargets-debug.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/sdl2/SDL2mainTargets-release.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/sdl2/sdlfind.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindVulkan.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/libzip/libzip-config-version.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/libzip/libzip-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/zlib/vcpkg-cmake-wrapper.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindZLIB.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/SelectLibraryConfigurations.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindBZip2.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/SelectLibraryConfigurations.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CheckSymbolExists.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakePushCheckState.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/openssl/vcpkg-cmake-wrapper.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindOpenSSL.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/SelectLibraryConfigurations.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/libzip/libzip-targets.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/libzip/libzip-targets-debug.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/libzip/libzip-targets-release.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/imgui/imgui-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeFindDependencyMacro.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindVulkan.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/freetype/vcpkg-cmake-wrapper.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/freetype/freetype-config-version.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/freetype/freetype-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/zlib/vcpkg-cmake-wrapper.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindZLIB.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/freetype/freetype-targets.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/freetype/freetype-targets-debug.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/freetype/freetype-targets-release.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/imgui/imgui-targets.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/imgui/imgui-targets-debug.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/imgui/imgui-targets-release.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/spdlog/spdlogConfigVersion.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/spdlog/spdlogConfig.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindThreads.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CheckLibraryExists.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CheckIncludeFile.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CheckCSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/Internal/CheckSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/fmt/fmt-config-version.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/fmt/fmt-config.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/fmt/fmt-targets.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/fmt/fmt-targets-debug.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/fmt/fmt-targets-release.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/spdlog/spdlogConfigTargets.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/spdlog/spdlogConfigTargets-debug.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/spdlog/spdlogConfigTargets-release.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/nlohmann_json/nlohmann_jsonConfigVersion.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/nlohmann_json/nlohmann_jsonConfig.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/nlohmann_json/nlohmann_jsonTargets.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/portaudio/portaudioConfigVersion.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/portaudio/portaudioConfig.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/portaudio/portaudioTargets.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/portaudio/portaudioTargets-debug.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/portaudio/portaudioTargets-release.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/zlib/vcpkg-cmake-wrapper.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindZLIB.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/unofficial-spirv-reflect/unofficial-spirv-reflect-config.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/unofficial-spirv-reflect/unofficial-spirv-reflect-config-debug.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/unofficial-spirv-reflect/unofficial-spirv-reflect-config-release.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/xbyak/xbyak-config-version.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/xbyak/xbyak-config.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/xbyak/xbyak-targets.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/capstone/capstone-config-version.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/capstone/capstone-config.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/capstone/capstone-targets.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/capstone/capstone-targets-debug.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/capstone/capstone-targets-release.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/tbb/TBBConfigVersion.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/tbb/TBBConfig.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeFindDependencyMacro.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindThreads.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CheckLibraryExists.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CheckIncludeFile.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CheckCSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/tbb/TBBTargets.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/tbb/TBBTargets-debug.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/tbb/TBBTargets-release.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/pugixml/pugixml-config-version.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/pugixml/pugixml-config.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/pugixml/pugixml-targets.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/pugixml/pugixml-targets-debug.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/pugixml/pugixml-targets-release.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/tsl-robin-map/tsl-robin-mapConfigVersion.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/tsl-robin-map/tsl-robin-mapConfig.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/tsl-robin-map/tsl-robin-mapTargets.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/toml11/toml11ConfigVersion.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/toml11/toml11Config.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/toml11/toml11Targets.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/Tracy/TracyConfig.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeFindDependencyMacro.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindThreads.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CheckLibraryExists.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CheckIncludeFile.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CheckCSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/Tracy/TracyTargets.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/Tracy/TracyTargets-debug.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/Tracy/TracyTargets-release.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/xxhash/xxHashConfigVersion.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/xxhash/xxHashConfig.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/xxhash/xxHashTargets.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/xxhash/xxHashTargets-debug.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/xxhash/xxHashTargets-release.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/zlib-ng/zlib-ng-config-version.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/zlib-ng/zlib-ng-config.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/zlib-ng/zlib-ng.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/zlib-ng/zlib-ng-config-version.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/zlib-ng/zlib-ng-config.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/zlib-ng/zlib-ng.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/zlib-ng/zlib-ng-debug.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/zlib-ng/zlib-ng-release.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/zydis/zydis-config-version.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/zydis/zydis-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/zycore/zycore-config-version.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/zycore/zycore-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/zycore/zyan-functions.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/zycore/zycore-targets.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/zycore/zycore-targets-debug.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/zycore/zycore-targets-release.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/zydis/zydis-targets.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/zydis/zydis-targets-debug.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/zydis/zydis-targets-release.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/openssl/vcpkg-cmake-wrapper.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindOpenSSL.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/SelectLibraryConfigurations.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/png/vcpkg-cmake-wrapper.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindPNG.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/zlib/vcpkg-cmake-wrapper.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindZLIB.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/SelectLibraryConfigurations.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/glslang/glslang-config-version.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/glslang/glslang-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/glslang/glslang-targets.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/glslang/glslang-targets-debug.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/glslang/glslang-targets-release.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/fmt/fmt-config-version.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/fmt/fmt-config.cmake"}, {"isExternal": true, "path": "D:/bin/llvm-project-main/llvm/cmake/modules/LLVM-Config.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/freetype/vcpkg-cmake-wrapper.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/freetype/freetype-config-version.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/freetype/freetype-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/freetype/freetype-targets.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/SelectLibraryConfigurations.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/plutosvg/plutosvgConfigVersion.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/plutosvg/plutosvgConfig.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/plutovg/plutovgConfigVersion.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/plutovg/plutovgConfig.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/plutovg/plutovgTargets.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/plutovg/plutovgTargets-debug.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/plutovg/plutovgTargets-release.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/plutosvg/plutosvgTargets.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/plutosvg/plutosvgTargets-debug.cmake"}, {"isExternal": true, "path": "D:/vcpkg/installed/x64-windows/share/plutosvg/plutosvgTargets-release.cmake"}], "kind": "cmakeFiles", "paths": {"build": "D:/sss/src/build", "source": "D:/sss/src"}, "version": {"major": 1, "minor": 1}}
#pragma once

#include "../ps4/ps4_emulator.h"
#include "../ps4/ps4_filesystem.h"
#include <cstdint>
#include <fstream>
#include <memory>
#include <mutex>
#include <string>
#include <unordered_map>
#include <vector>

// Struct packing for different compilers
#ifdef _MSC_VER
#pragma pack(push, 1)
#define PACKED_STRUCT_START
#define PACKED_STRUCT_END
#define PACKED_STRUCT_RESTORE #pragma pack(pop)
#elif defined(__GNUC__)
#define PACKED_STRUCT_START
#define PACKED_STRUCT_END __attribute__((packed))
#define PACKED_STRUCT_RESTORE
#else
#define PACKED_STRUCT_START
#define PACKED_STRUCT_END
#define PACKED_STRUCT_RESTORE
#endif

namespace ps4 {

class PS4Filesystem;
class PS4Emulator;

/**
 * @brief Exception thrown during PKG operations.
 */
class PKGException : public std::runtime_error {
public:
  explicit PKGException(const std::string &message)
      : std::runtime_error(message) {}
};

/**
 * @brief PS4 Package file header structure.
 * @details Matches the actual PS4 PKG format as documented in PS4 Developer
 * Wiki. Header uses big-endian byte order.
 */
PACKED_STRUCT_START
struct PKGHeader {
  // Core header (0x000-0x020)
  uint32_t magic;           ///< 0x000 - Magic number: 0x7F434E54 (big-endian)
  uint32_t type;            ///< 0x004 - Package type
  uint32_t unknown_0x008;   ///< 0x008 - Unknown field
  uint32_t file_count;      ///< 0x00C - Number of files in package
  uint32_t entry_count;     ///< 0x010 - Number of entries
  uint16_t sc_entry_count;  ///< 0x014 - SC entry count
  uint16_t entry_count_2;   ///< 0x016 - Entry count (duplicate)
  uint32_t table_offset;    ///< 0x018 - File table offset
  uint32_t entry_data_size; ///< 0x01C - Entry data size

  // Body information (0x020-0x040)
  uint64_t body_offset;    ///< 0x020 - Offset of PKG entries
  uint64_t body_size;      ///< 0x028 - Length of all PKG entries
  uint64_t content_offset; ///< 0x030 - Content offset
  uint64_t content_size;   ///< 0x038 - Content size

  // Content identification (0x040-0x070)
  char content_id[0x24];   ///< 0x040 - Content ID (36 bytes)
  char padding_0x064[0xC]; ///< 0x064 - Padding
  // DRM and content info (0x070-0x0A0)
  uint32_t drm_type;         ///< 0x070 - DRM type
  uint32_t content_type;     ///< 0x074 - Content type
  uint32_t content_flags;    ///< 0x078 - Content flags
  uint32_t promote_size;     ///< 0x07C - Promote size
  uint32_t version_date;     ///< 0x080 - Version date
  uint32_t version_hash;     ///< 0x084 - Version hash
  uint32_t unknown_0x088;    ///< 0x088 - Unknown
  uint32_t unknown_0x08C;    ///< 0x08C - Unknown
  uint64_t iro_tag;          ///< 0x090 - IRO tag
  uint32_t drm_type_version; ///< 0x098 - DRM type version
  // Additional fields to complete the header to 0x100
  char reserved[0x58]; ///< 0x09C-0x100 - Reserved space
} PACKED_STRUCT_END;

/**
 * @brief PS4 Package table entry structure.
 */
PACKED_STRUCT_START
struct PKGTableEntry {
  uint32_t id;              ///< Entry ID
  uint32_t filename_offset; ///< Filename offset (or 0 for ID-based)
  uint32_t flags1;          ///< Flags including encrypted flag
  uint32_t flags2;          ///< Flags including encryption key index
  uint32_t offset;          ///< Offset into PKG to find the file
  uint32_t size;            ///< Size of the file
  uint64_t padding;         ///< Blank padding
} PACKED_STRUCT_END;
/**
 * @brief Legacy PKG item entry (for compatibility).
 */
PACKED_STRUCT_START
struct PKGItem {
  uint32_t id;
  uint32_t nameOffset; ///< Name string offset
  uint32_t nameSize;   ///< Name string size
  uint64_t dataOffset; ///< Data offset
  uint64_t dataSize;   ///< Data size
  uint8_t type;        ///< Add missing type field
  uint8_t flags;       ///< Item flags
  uint8_t reserved[6]; ///< Reserved bytes
} PACKED_STRUCT_END;

/**
 * @brief SFO (System File Object) parameter entry.
 */
PACKED_STRUCT_START
struct SFOParam {
  uint16_t keyOffset;  ///< Key string offset
  uint16_t format;     ///< Data format (utf8, uint32, etc.)
  uint32_t length;     ///< Data length
  uint32_t maxLength;  ///< Maximum data length
  uint32_t dataOffset; ///< Data offset
} PACKED_STRUCT_END;

/**
 * @brief SFO (System File Object) header structure.
 */
PACKED_STRUCT_START
struct SFOHeader {
  uint32_t magic;           ///< Magic: 0x46535000 (\0PSF)
  uint32_t version;         ///< SFO version
  uint32_t keyTableOffset;  ///< Key table offset
  uint32_t dataTableOffset; ///< Data table offset
  uint32_t paramCount;      ///< Number of parameters
} PACKED_STRUCT_END;

// PKG constants
const uint32_t PKG_MAGIC = 0x7F434E54; ///< PKG magic number (big-endian)
const uint32_t SFO_MAGIC = 0x46535000; ///< SFO magic number (\0PSF)

// PKG types
const uint32_t PKG_TYPE_GAME = 0x0001;   ///< Game package
const uint32_t PKG_TYPE_UPDATE = 0x0002; ///< Update package
const uint32_t PKG_TYPE_DLC = 0x0003;    ///< DLC package
const uint32_t PKG_TYPE_THEME = 0x0004;  ///< Theme package

// PKG content types
const uint32_t PKG_CONTENT_TYPE_GAME = 0x01;   ///< Game content
const uint32_t PKG_CONTENT_TYPE_UPDATE = 0x02; ///< Update content
const uint32_t PKG_CONTENT_TYPE_DLC = 0x03;    ///< DLC content

// Endianness conversion utilities for PS4 PKG files (big-endian)
namespace PKGEndian {
/**
 * @brief Converts a 32-bit value from big-endian to host byte order.
 */
inline uint32_t be32toh(uint32_t big_endian_32bits) {
#ifdef _WIN32
  return _byteswap_ulong(big_endian_32bits);
#else
  return __builtin_bswap32(big_endian_32bits);
#endif
}

/**
 * @brief Converts a 64-bit value from big-endian to host byte order.
 */
inline uint64_t be64toh(uint64_t big_endian_64bits) {
#ifdef _WIN32
  return _byteswap_uint64(big_endian_64bits);
#else
  return __builtin_bswap64(big_endian_64bits);
#endif
}

/**
 * @brief Converts a 16-bit value from big-endian to host byte order.
 */
inline uint16_t be16toh(uint16_t big_endian_16bits) {
#ifdef _WIN32
  return _byteswap_ushort(big_endian_16bits);
#else
  return __builtin_bswap16(big_endian_16bits);
#endif
}
} // namespace PKGEndian

/**
 * @brief PS4 Package installer and manager.
 * @details Handles installation of PS4 PKG files, including games, updates, and
 * DLC.
 */
class PKGInstaller {
public:
  /**
   * @brief Constructs a PKG installer.
   * @param filesystem Pointer to PS4 filesystem
   * @param emulator Pointer to PS4 emulator (optional)
   */
  explicit PKGInstaller(PS4Filesystem *filesystem,
                        PS4Emulator *emulator = nullptr);

  /**
   * @brief Destructor.
   */
  ~PKGInstaller();

  /**
   * @brief Installs a PKG file.
   * @param pkgPath Path to the PKG file
   * @param installPath Target installation path (defaults to /app0)
   * @return True on success, false otherwise
   */
  bool InstallPKG(const std::string &pkgPath,
                  const std::string &installPath = "/app0");

  /**
   * @brief Installs an update PKG and merges with base game.
   * @param updatePkgPath Path to the update PKG file
   * @param baseGamePath Path to the base game installation
   * @return True on success, false otherwise
   */
  bool InstallUpdatePKG(const std::string &updatePkgPath,
                        const std::string &baseGamePath);

  /**
   * @brief Validates a PKG file integrity.
   * @param pkgPath Path to the PKG file
   * @return True if valid, false otherwise
   */
  bool ValidatePKG(const std::string &pkgPath);

  /**
   * @brief Detects and analyzes PKG file format.
   * @param pkgPath Path to the PKG file
   * @return True if appears to be a valid PKG, false otherwise
   */
  bool DetectAndAnalyzePKG(const std::string &pkgPath);

  /**
   * @brief Extracts PKG contents to a directory.
   * @param pkgPath Path to the PKG file
   * @param extractPath Target extraction directory
   * @return True on success, false otherwise
   */
  bool ExtractPKG(const std::string &pkgPath, const std::string &extractPath);

  /**
   * @brief Gets PKG information without installing.
   * @param pkgPath Path to the PKG file
   * @param contentId Output content ID
   * @param version Output version string
   * @param title Output game title
   * @return True on success, false otherwise
   */
  bool GetPKGInfo(const std::string &pkgPath, std::string &contentId,
                  std::string &version, std::string &title);

  /**
   * @brief Lists installed packages.
   * @return Vector of installed content IDs
   */
  std::vector<std::string> ListInstalledPackages() const;

  /**
   * @brief Saves the installed packages list to disk.
   * @return True on success, false otherwise
   */
  bool SaveInstalledPackages() const;

  /**
   * @brief Loads the installed packages list from disk.
   * @return True on success, false otherwise
   */
  bool LoadInstalledPackages();

  /**
   * @brief Uninstalls a package.
   * @param contentId Content ID to uninstall
   * @return True on success, false otherwise
   */
  bool UninstallPackage(const std::string &contentId);

  // Returns the virtual install root for a given content ID
  const std::string &
  GetInstallPath(const std::string &contentId) const noexcept;

private:
  /**
   * @brief Reads and validates PKG header.
   * @param file Input file stream
   * @param header Output header structure
   * @return True if valid, false otherwise
   */
  bool ReadPKGHeader(std::ifstream &file, PKGHeader &header);

  /**
   * @brief Reads PKG items table.
   * @param file Input file stream
   * @param header PKG header
   * @param items Output items vector
   * @return True on success, false otherwise
   */
  bool ReadPKGItems(std::ifstream &file, const PKGHeader &header,
                    std::vector<PKGItem> &items);

  /**
   * @brief Extracts a specific item from PKG.
   * @param file Input file stream
   * @param item Item to extract
   * @param nameTable Name table for resolving names
   * @param data Output data buffer
   * @return True on success, false otherwise
   */
  bool ExtractPKGItem(std::ifstream &file, const PKGItem &item,
                      const std::vector<uint8_t> &nameTable,
                      std::vector<uint8_t> &data);

  /**
   * @brief Parses SFO (System File Object) data.
   * @param sfoData Raw SFO data
   * @param params Output parameter map
   * @return True on success, false otherwise
   */
  bool ParseSFO(const std::vector<uint8_t> &sfoData,
                std::unordered_map<std::string, std::string> &params);

  /**
   * @brief Merges update files with base game files.
   * @param baseFiles Base game files map
   * @param updateFiles Update files map
   * @return True on success, false otherwise
   */
  bool MergeUpdateFiles(
      std::unordered_map<std::string, std::vector<uint8_t>> &baseFiles,
      const std::unordered_map<std::string, std::vector<uint8_t>> &updateFiles);

  /**
   * @brief Computes SHA-256 hash of data.
   * @param data Input data
   * @return SHA-256 hash as hex string
   */
  std::string ComputeSHA256(const std::vector<uint8_t> &data);

  /**
   * @brief Verifies package integrity.
   * @param header PKG header
   * @param packageData Full package data
   * @return True if integrity check passes
   */
  bool VerifyPackageIntegrity(const PKGHeader &header,
                              const std::vector<uint8_t> &packageData);

  /**
   * @brief Creates installation directory structure.
   * @param installPath Base installation path
   * @param contentId Content ID for directory naming
   * @return Full installation path
   */
  std::string CreateInstallDirectory(const std::string &installPath,
                                     const std::string &contentId);

  /**
   * @brief Reads name table from PKG.
   * @param file Input file stream
   * @param header PKG header
   * @param nameTable Output name table
   * @return True on success, false otherwise
   */
  bool ReadNameTable(std::ifstream &file, const PKGHeader &header,
                     std::vector<uint8_t> &nameTable);

  /**
   * @brief Gets item name from name table.
   * @param nameTable Name table data
   * @param nameOffset Offset in name table
   * @param nameSize Size of name string
   * @return Item name string
   */
  std::string GetItemName(const std::vector<uint8_t> &nameTable,
                          uint32_t nameOffset, uint32_t nameSize);

  /**
   * @brief Extracts safe filename for an item.
   * @param item PKG item
   * @param nameTable Name table
   * @param itemIndex Index of the item
   * @return Safe filename string
   */
  std::string ExtractSafeFilename(const PKGItem &item,
                                  const std::vector<uint8_t> &nameTable,
                                  int itemIndex);

  /**
   * @brief Sanitizes a file name by removing invalid characters.
   * @param filename Original file name
   * @return Sanitized file name
   */
  std::string SanitizeFileName(const std::string &filename);

  /**
   * @brief Checks if data is binary (not text).
   * @param data Data to check
   * @return True if binary, false if text
   */
  bool IsBinaryData(const std::vector<uint8_t> &data) const;

  PS4Filesystem *m_filesystem; ///< Pointer to PS4 filesystem
  PS4Emulator *m_emulator;     ///< Pointer to PS4 emulator
  std::string m_installRoot;   ///< Root installation directory
  std::unordered_map<std::string, std::string>
      m_installedPackages;   ///< Content ID -> Install path
  std::mutex m_installMutex; ///< Mutex for installation synchronization
};

} // namespace ps4

#ifdef _MSC_VER
#pragma pack(pop)
#endif
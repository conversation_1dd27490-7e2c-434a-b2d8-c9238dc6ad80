﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <PropertyGroup>
    <ResolveNugetPackages>false</ResolveNugetPackages>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{E3F5E286-8887-3765-AC66-DA0A90D5C421}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>ALL_BUILD</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="D:\sss\src\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule D:/sss/src/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/sss/src -BD:/sss/src/build --check-stamp-file D:/sss/src/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeCInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeCXXInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeDependentOption.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeGenericSystem.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeLanguageInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakePushCheckState.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeRCInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CheckCSourceCompiles.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CheckIncludeFile.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CheckLibraryExists.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CheckSymbolExists.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\MSVC-C.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\MSVC.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\FeatureSummary.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\FindBZip2.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\FindOpenSSL.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\FindPNG.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\FindPackageMessage.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\FindThreads.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\FindVulkan.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\FindZLIB.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Platform\Windows-MSVC-C.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Platform\Windows.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Platform\WindowsPaths.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\SelectLibraryConfigurations.cmake;D:\bin\llvm-project-main\llvm\cmake\modules\LLVM-Config.cmake;D:\sss\src\build\CMakeFiles\3.30.5-msvc23\CMakeCCompiler.cmake;D:\sss\src\build\CMakeFiles\3.30.5-msvc23\CMakeCXXCompiler.cmake;D:\sss\src\build\CMakeFiles\3.30.5-msvc23\CMakeRCCompiler.cmake;D:\sss\src\build\CMakeFiles\3.30.5-msvc23\CMakeSystem.cmake;D:\vcpkg\installed\x64-windows\share\Tracy\TracyConfig.cmake;D:\vcpkg\installed\x64-windows\share\Tracy\TracyTargets-debug.cmake;D:\vcpkg\installed\x64-windows\share\Tracy\TracyTargets-release.cmake;D:\vcpkg\installed\x64-windows\share\Tracy\TracyTargets.cmake;D:\vcpkg\installed\x64-windows\share\capstone\capstone-config-version.cmake;D:\vcpkg\installed\x64-windows\share\capstone\capstone-config.cmake;D:\vcpkg\installed\x64-windows\share\capstone\capstone-targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\capstone\capstone-targets-release.cmake;D:\vcpkg\installed\x64-windows\share\capstone\capstone-targets.cmake;D:\vcpkg\installed\x64-windows\share\fmt\fmt-config-version.cmake;D:\vcpkg\installed\x64-windows\share\fmt\fmt-config.cmake;D:\vcpkg\installed\x64-windows\share\fmt\fmt-targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\fmt\fmt-targets-release.cmake;D:\vcpkg\installed\x64-windows\share\fmt\fmt-targets.cmake;D:\vcpkg\installed\x64-windows\share\freetype\freetype-config-version.cmake;D:\vcpkg\installed\x64-windows\share\freetype\freetype-config.cmake;D:\vcpkg\installed\x64-windows\share\freetype\freetype-targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\freetype\freetype-targets-release.cmake;D:\vcpkg\installed\x64-windows\share\freetype\freetype-targets.cmake;D:\vcpkg\installed\x64-windows\share\freetype\vcpkg-cmake-wrapper.cmake;D:\vcpkg\installed\x64-windows\share\glslang\glslang-config-version.cmake;D:\vcpkg\installed\x64-windows\share\glslang\glslang-config.cmake;D:\vcpkg\installed\x64-windows\share\glslang\glslang-targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\glslang\glslang-targets-release.cmake;D:\vcpkg\installed\x64-windows\share\glslang\glslang-targets.cmake;D:\vcpkg\installed\x64-windows\share\imgui\imgui-config.cmake;D:\vcpkg\installed\x64-windows\share\imgui\imgui-targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\imgui\imgui-targets-release.cmake;D:\vcpkg\installed\x64-windows\share\imgui\imgui-targets.cmake;D:\vcpkg\installed\x64-windows\share\libzip\libzip-config-version.cmake;D:\vcpkg\installed\x64-windows\share\libzip\libzip-config.cmake;D:\vcpkg\installed\x64-windows\share\libzip\libzip-targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\libzip\libzip-targets-release.cmake;D:\vcpkg\installed\x64-windows\share\libzip\libzip-targets.cmake;D:\vcpkg\installed\x64-windows\share\nlohmann_json\nlohmann_jsonConfig.cmake;D:\vcpkg\installed\x64-windows\share\nlohmann_json\nlohmann_jsonConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\nlohmann_json\nlohmann_jsonTargets.cmake;D:\vcpkg\installed\x64-windows\share\openssl\vcpkg-cmake-wrapper.cmake;D:\vcpkg\installed\x64-windows\share\plutosvg\plutosvgConfig.cmake;D:\vcpkg\installed\x64-windows\share\plutosvg\plutosvgConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\plutosvg\plutosvgTargets-debug.cmake;D:\vcpkg\installed\x64-windows\share\plutosvg\plutosvgTargets-release.cmake;D:\vcpkg\installed\x64-windows\share\plutosvg\plutosvgTargets.cmake;D:\vcpkg\installed\x64-windows\share\plutovg\plutovgConfig.cmake;D:\vcpkg\installed\x64-windows\share\plutovg\plutovgConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\plutovg\plutovgTargets-debug.cmake;D:\vcpkg\installed\x64-windows\share\plutovg\plutovgTargets-release.cmake;D:\vcpkg\installed\x64-windows\share\plutovg\plutovgTargets.cmake;D:\vcpkg\installed\x64-windows\share\png\vcpkg-cmake-wrapper.cmake;D:\vcpkg\installed\x64-windows\share\portaudio\portaudioConfig.cmake;D:\vcpkg\installed\x64-windows\share\portaudio\portaudioConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\portaudio\portaudioTargets-debug.cmake;D:\vcpkg\installed\x64-windows\share\portaudio\portaudioTargets-release.cmake;D:\vcpkg\installed\x64-windows\share\portaudio\portaudioTargets.cmake;D:\vcpkg\installed\x64-windows\share\pugixml\pugixml-config-version.cmake;D:\vcpkg\installed\x64-windows\share\pugixml\pugixml-config.cmake;D:\vcpkg\installed\x64-windows\share\pugixml\pugixml-targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\pugixml\pugixml-targets-release.cmake;D:\vcpkg\installed\x64-windows\share\pugixml\pugixml-targets.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\SDL2Config.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\SDL2ConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\SDL2Targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\SDL2Targets-release.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\SDL2Targets.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\SDL2mainTargets-debug.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\SDL2mainTargets-release.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\SDL2mainTargets.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\sdlfind.cmake;D:\vcpkg\installed\x64-windows\share\spdlog\spdlogConfig.cmake;D:\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets-debug.cmake;D:\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets-release.cmake;D:\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets.cmake;D:\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\tbb\TBBConfig.cmake;D:\vcpkg\installed\x64-windows\share\tbb\TBBConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\tbb\TBBTargets-debug.cmake;D:\vcpkg\installed\x64-windows\share\tbb\TBBTargets-release.cmake;D:\vcpkg\installed\x64-windows\share\tbb\TBBTargets.cmake;D:\vcpkg\installed\x64-windows\share\toml11\toml11Config.cmake;D:\vcpkg\installed\x64-windows\share\toml11\toml11ConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\toml11\toml11Targets.cmake;D:\vcpkg\installed\x64-windows\share\tsl-robin-map\tsl-robin-mapConfig.cmake;D:\vcpkg\installed\x64-windows\share\tsl-robin-map\tsl-robin-mapConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\tsl-robin-map\tsl-robin-mapTargets.cmake;D:\vcpkg\installed\x64-windows\share\unofficial-spirv-reflect\unofficial-spirv-reflect-config-debug.cmake;D:\vcpkg\installed\x64-windows\share\unofficial-spirv-reflect\unofficial-spirv-reflect-config-release.cmake;D:\vcpkg\installed\x64-windows\share\unofficial-spirv-reflect\unofficial-spirv-reflect-config.cmake;D:\vcpkg\installed\x64-windows\share\xbyak\xbyak-config-version.cmake;D:\vcpkg\installed\x64-windows\share\xbyak\xbyak-config.cmake;D:\vcpkg\installed\x64-windows\share\xbyak\xbyak-targets.cmake;D:\vcpkg\installed\x64-windows\share\xxhash\xxHashConfig.cmake;D:\vcpkg\installed\x64-windows\share\xxhash\xxHashConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\xxhash\xxHashTargets-debug.cmake;D:\vcpkg\installed\x64-windows\share\xxhash\xxHashTargets-release.cmake;D:\vcpkg\installed\x64-windows\share\xxhash\xxHashTargets.cmake;D:\vcpkg\installed\x64-windows\share\zlib-ng\zlib-ng-config-version.cmake;D:\vcpkg\installed\x64-windows\share\zlib-ng\zlib-ng-config.cmake;D:\vcpkg\installed\x64-windows\share\zlib-ng\zlib-ng-debug.cmake;D:\vcpkg\installed\x64-windows\share\zlib-ng\zlib-ng-release.cmake;D:\vcpkg\installed\x64-windows\share\zlib-ng\zlib-ng.cmake;D:\vcpkg\installed\x64-windows\share\zlib\vcpkg-cmake-wrapper.cmake;D:\vcpkg\installed\x64-windows\share\zycore\zyan-functions.cmake;D:\vcpkg\installed\x64-windows\share\zycore\zycore-config-version.cmake;D:\vcpkg\installed\x64-windows\share\zycore\zycore-config.cmake;D:\vcpkg\installed\x64-windows\share\zycore\zycore-targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\zycore\zycore-targets-release.cmake;D:\vcpkg\installed\x64-windows\share\zycore\zycore-targets.cmake;D:\vcpkg\installed\x64-windows\share\zydis\zydis-config-version.cmake;D:\vcpkg\installed\x64-windows\share\zydis\zydis-config.cmake;D:\vcpkg\installed\x64-windows\share\zydis\zydis-targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\zydis\zydis-targets-release.cmake;D:\vcpkg\installed\x64-windows\share\zydis\zydis-targets.cmake;D:\vcpkg\scripts\buildsystems\vcpkg.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\sss\src\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule D:/sss/src/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/sss/src -BD:/sss/src/build --check-stamp-file D:/sss/src/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeCInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeCXXInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeDependentOption.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeGenericSystem.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeLanguageInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakePushCheckState.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeRCInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CheckCSourceCompiles.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CheckIncludeFile.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CheckLibraryExists.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CheckSymbolExists.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\MSVC-C.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\MSVC.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\FeatureSummary.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\FindBZip2.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\FindOpenSSL.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\FindPNG.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\FindPackageMessage.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\FindThreads.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\FindVulkan.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\FindZLIB.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Platform\Windows-MSVC-C.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Platform\Windows.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Platform\WindowsPaths.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\SelectLibraryConfigurations.cmake;D:\bin\llvm-project-main\llvm\cmake\modules\LLVM-Config.cmake;D:\sss\src\build\CMakeFiles\3.30.5-msvc23\CMakeCCompiler.cmake;D:\sss\src\build\CMakeFiles\3.30.5-msvc23\CMakeCXXCompiler.cmake;D:\sss\src\build\CMakeFiles\3.30.5-msvc23\CMakeRCCompiler.cmake;D:\sss\src\build\CMakeFiles\3.30.5-msvc23\CMakeSystem.cmake;D:\vcpkg\installed\x64-windows\share\Tracy\TracyConfig.cmake;D:\vcpkg\installed\x64-windows\share\Tracy\TracyTargets-debug.cmake;D:\vcpkg\installed\x64-windows\share\Tracy\TracyTargets-release.cmake;D:\vcpkg\installed\x64-windows\share\Tracy\TracyTargets.cmake;D:\vcpkg\installed\x64-windows\share\capstone\capstone-config-version.cmake;D:\vcpkg\installed\x64-windows\share\capstone\capstone-config.cmake;D:\vcpkg\installed\x64-windows\share\capstone\capstone-targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\capstone\capstone-targets-release.cmake;D:\vcpkg\installed\x64-windows\share\capstone\capstone-targets.cmake;D:\vcpkg\installed\x64-windows\share\fmt\fmt-config-version.cmake;D:\vcpkg\installed\x64-windows\share\fmt\fmt-config.cmake;D:\vcpkg\installed\x64-windows\share\fmt\fmt-targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\fmt\fmt-targets-release.cmake;D:\vcpkg\installed\x64-windows\share\fmt\fmt-targets.cmake;D:\vcpkg\installed\x64-windows\share\freetype\freetype-config-version.cmake;D:\vcpkg\installed\x64-windows\share\freetype\freetype-config.cmake;D:\vcpkg\installed\x64-windows\share\freetype\freetype-targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\freetype\freetype-targets-release.cmake;D:\vcpkg\installed\x64-windows\share\freetype\freetype-targets.cmake;D:\vcpkg\installed\x64-windows\share\freetype\vcpkg-cmake-wrapper.cmake;D:\vcpkg\installed\x64-windows\share\glslang\glslang-config-version.cmake;D:\vcpkg\installed\x64-windows\share\glslang\glslang-config.cmake;D:\vcpkg\installed\x64-windows\share\glslang\glslang-targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\glslang\glslang-targets-release.cmake;D:\vcpkg\installed\x64-windows\share\glslang\glslang-targets.cmake;D:\vcpkg\installed\x64-windows\share\imgui\imgui-config.cmake;D:\vcpkg\installed\x64-windows\share\imgui\imgui-targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\imgui\imgui-targets-release.cmake;D:\vcpkg\installed\x64-windows\share\imgui\imgui-targets.cmake;D:\vcpkg\installed\x64-windows\share\libzip\libzip-config-version.cmake;D:\vcpkg\installed\x64-windows\share\libzip\libzip-config.cmake;D:\vcpkg\installed\x64-windows\share\libzip\libzip-targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\libzip\libzip-targets-release.cmake;D:\vcpkg\installed\x64-windows\share\libzip\libzip-targets.cmake;D:\vcpkg\installed\x64-windows\share\nlohmann_json\nlohmann_jsonConfig.cmake;D:\vcpkg\installed\x64-windows\share\nlohmann_json\nlohmann_jsonConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\nlohmann_json\nlohmann_jsonTargets.cmake;D:\vcpkg\installed\x64-windows\share\openssl\vcpkg-cmake-wrapper.cmake;D:\vcpkg\installed\x64-windows\share\plutosvg\plutosvgConfig.cmake;D:\vcpkg\installed\x64-windows\share\plutosvg\plutosvgConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\plutosvg\plutosvgTargets-debug.cmake;D:\vcpkg\installed\x64-windows\share\plutosvg\plutosvgTargets-release.cmake;D:\vcpkg\installed\x64-windows\share\plutosvg\plutosvgTargets.cmake;D:\vcpkg\installed\x64-windows\share\plutovg\plutovgConfig.cmake;D:\vcpkg\installed\x64-windows\share\plutovg\plutovgConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\plutovg\plutovgTargets-debug.cmake;D:\vcpkg\installed\x64-windows\share\plutovg\plutovgTargets-release.cmake;D:\vcpkg\installed\x64-windows\share\plutovg\plutovgTargets.cmake;D:\vcpkg\installed\x64-windows\share\png\vcpkg-cmake-wrapper.cmake;D:\vcpkg\installed\x64-windows\share\portaudio\portaudioConfig.cmake;D:\vcpkg\installed\x64-windows\share\portaudio\portaudioConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\portaudio\portaudioTargets-debug.cmake;D:\vcpkg\installed\x64-windows\share\portaudio\portaudioTargets-release.cmake;D:\vcpkg\installed\x64-windows\share\portaudio\portaudioTargets.cmake;D:\vcpkg\installed\x64-windows\share\pugixml\pugixml-config-version.cmake;D:\vcpkg\installed\x64-windows\share\pugixml\pugixml-config.cmake;D:\vcpkg\installed\x64-windows\share\pugixml\pugixml-targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\pugixml\pugixml-targets-release.cmake;D:\vcpkg\installed\x64-windows\share\pugixml\pugixml-targets.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\SDL2Config.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\SDL2ConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\SDL2Targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\SDL2Targets-release.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\SDL2Targets.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\SDL2mainTargets-debug.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\SDL2mainTargets-release.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\SDL2mainTargets.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\sdlfind.cmake;D:\vcpkg\installed\x64-windows\share\spdlog\spdlogConfig.cmake;D:\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets-debug.cmake;D:\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets-release.cmake;D:\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets.cmake;D:\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\tbb\TBBConfig.cmake;D:\vcpkg\installed\x64-windows\share\tbb\TBBConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\tbb\TBBTargets-debug.cmake;D:\vcpkg\installed\x64-windows\share\tbb\TBBTargets-release.cmake;D:\vcpkg\installed\x64-windows\share\tbb\TBBTargets.cmake;D:\vcpkg\installed\x64-windows\share\toml11\toml11Config.cmake;D:\vcpkg\installed\x64-windows\share\toml11\toml11ConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\toml11\toml11Targets.cmake;D:\vcpkg\installed\x64-windows\share\tsl-robin-map\tsl-robin-mapConfig.cmake;D:\vcpkg\installed\x64-windows\share\tsl-robin-map\tsl-robin-mapConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\tsl-robin-map\tsl-robin-mapTargets.cmake;D:\vcpkg\installed\x64-windows\share\unofficial-spirv-reflect\unofficial-spirv-reflect-config-debug.cmake;D:\vcpkg\installed\x64-windows\share\unofficial-spirv-reflect\unofficial-spirv-reflect-config-release.cmake;D:\vcpkg\installed\x64-windows\share\unofficial-spirv-reflect\unofficial-spirv-reflect-config.cmake;D:\vcpkg\installed\x64-windows\share\xbyak\xbyak-config-version.cmake;D:\vcpkg\installed\x64-windows\share\xbyak\xbyak-config.cmake;D:\vcpkg\installed\x64-windows\share\xbyak\xbyak-targets.cmake;D:\vcpkg\installed\x64-windows\share\xxhash\xxHashConfig.cmake;D:\vcpkg\installed\x64-windows\share\xxhash\xxHashConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\xxhash\xxHashTargets-debug.cmake;D:\vcpkg\installed\x64-windows\share\xxhash\xxHashTargets-release.cmake;D:\vcpkg\installed\x64-windows\share\xxhash\xxHashTargets.cmake;D:\vcpkg\installed\x64-windows\share\zlib-ng\zlib-ng-config-version.cmake;D:\vcpkg\installed\x64-windows\share\zlib-ng\zlib-ng-config.cmake;D:\vcpkg\installed\x64-windows\share\zlib-ng\zlib-ng-debug.cmake;D:\vcpkg\installed\x64-windows\share\zlib-ng\zlib-ng-release.cmake;D:\vcpkg\installed\x64-windows\share\zlib-ng\zlib-ng.cmake;D:\vcpkg\installed\x64-windows\share\zlib\vcpkg-cmake-wrapper.cmake;D:\vcpkg\installed\x64-windows\share\zycore\zyan-functions.cmake;D:\vcpkg\installed\x64-windows\share\zycore\zycore-config-version.cmake;D:\vcpkg\installed\x64-windows\share\zycore\zycore-config.cmake;D:\vcpkg\installed\x64-windows\share\zycore\zycore-targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\zycore\zycore-targets-release.cmake;D:\vcpkg\installed\x64-windows\share\zycore\zycore-targets.cmake;D:\vcpkg\installed\x64-windows\share\zydis\zydis-config-version.cmake;D:\vcpkg\installed\x64-windows\share\zydis\zydis-config.cmake;D:\vcpkg\installed\x64-windows\share\zydis\zydis-targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\zydis\zydis-targets-release.cmake;D:\vcpkg\installed\x64-windows\share\zydis\zydis-targets.cmake;D:\vcpkg\scripts\buildsystems\vcpkg.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\sss\src\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule D:/sss/src/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/sss/src -BD:/sss/src/build --check-stamp-file D:/sss/src/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeCInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeCXXInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeDependentOption.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeGenericSystem.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeLanguageInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakePushCheckState.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeRCInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CheckCSourceCompiles.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CheckIncludeFile.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CheckLibraryExists.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CheckSymbolExists.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\MSVC-C.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\MSVC.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\FeatureSummary.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\FindBZip2.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\FindOpenSSL.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\FindPNG.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\FindPackageMessage.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\FindThreads.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\FindVulkan.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\FindZLIB.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Platform\Windows-MSVC-C.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Platform\Windows.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Platform\WindowsPaths.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\SelectLibraryConfigurations.cmake;D:\bin\llvm-project-main\llvm\cmake\modules\LLVM-Config.cmake;D:\sss\src\build\CMakeFiles\3.30.5-msvc23\CMakeCCompiler.cmake;D:\sss\src\build\CMakeFiles\3.30.5-msvc23\CMakeCXXCompiler.cmake;D:\sss\src\build\CMakeFiles\3.30.5-msvc23\CMakeRCCompiler.cmake;D:\sss\src\build\CMakeFiles\3.30.5-msvc23\CMakeSystem.cmake;D:\vcpkg\installed\x64-windows\share\Tracy\TracyConfig.cmake;D:\vcpkg\installed\x64-windows\share\Tracy\TracyTargets-debug.cmake;D:\vcpkg\installed\x64-windows\share\Tracy\TracyTargets-release.cmake;D:\vcpkg\installed\x64-windows\share\Tracy\TracyTargets.cmake;D:\vcpkg\installed\x64-windows\share\capstone\capstone-config-version.cmake;D:\vcpkg\installed\x64-windows\share\capstone\capstone-config.cmake;D:\vcpkg\installed\x64-windows\share\capstone\capstone-targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\capstone\capstone-targets-release.cmake;D:\vcpkg\installed\x64-windows\share\capstone\capstone-targets.cmake;D:\vcpkg\installed\x64-windows\share\fmt\fmt-config-version.cmake;D:\vcpkg\installed\x64-windows\share\fmt\fmt-config.cmake;D:\vcpkg\installed\x64-windows\share\fmt\fmt-targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\fmt\fmt-targets-release.cmake;D:\vcpkg\installed\x64-windows\share\fmt\fmt-targets.cmake;D:\vcpkg\installed\x64-windows\share\freetype\freetype-config-version.cmake;D:\vcpkg\installed\x64-windows\share\freetype\freetype-config.cmake;D:\vcpkg\installed\x64-windows\share\freetype\freetype-targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\freetype\freetype-targets-release.cmake;D:\vcpkg\installed\x64-windows\share\freetype\freetype-targets.cmake;D:\vcpkg\installed\x64-windows\share\freetype\vcpkg-cmake-wrapper.cmake;D:\vcpkg\installed\x64-windows\share\glslang\glslang-config-version.cmake;D:\vcpkg\installed\x64-windows\share\glslang\glslang-config.cmake;D:\vcpkg\installed\x64-windows\share\glslang\glslang-targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\glslang\glslang-targets-release.cmake;D:\vcpkg\installed\x64-windows\share\glslang\glslang-targets.cmake;D:\vcpkg\installed\x64-windows\share\imgui\imgui-config.cmake;D:\vcpkg\installed\x64-windows\share\imgui\imgui-targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\imgui\imgui-targets-release.cmake;D:\vcpkg\installed\x64-windows\share\imgui\imgui-targets.cmake;D:\vcpkg\installed\x64-windows\share\libzip\libzip-config-version.cmake;D:\vcpkg\installed\x64-windows\share\libzip\libzip-config.cmake;D:\vcpkg\installed\x64-windows\share\libzip\libzip-targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\libzip\libzip-targets-release.cmake;D:\vcpkg\installed\x64-windows\share\libzip\libzip-targets.cmake;D:\vcpkg\installed\x64-windows\share\nlohmann_json\nlohmann_jsonConfig.cmake;D:\vcpkg\installed\x64-windows\share\nlohmann_json\nlohmann_jsonConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\nlohmann_json\nlohmann_jsonTargets.cmake;D:\vcpkg\installed\x64-windows\share\openssl\vcpkg-cmake-wrapper.cmake;D:\vcpkg\installed\x64-windows\share\plutosvg\plutosvgConfig.cmake;D:\vcpkg\installed\x64-windows\share\plutosvg\plutosvgConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\plutosvg\plutosvgTargets-debug.cmake;D:\vcpkg\installed\x64-windows\share\plutosvg\plutosvgTargets-release.cmake;D:\vcpkg\installed\x64-windows\share\plutosvg\plutosvgTargets.cmake;D:\vcpkg\installed\x64-windows\share\plutovg\plutovgConfig.cmake;D:\vcpkg\installed\x64-windows\share\plutovg\plutovgConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\plutovg\plutovgTargets-debug.cmake;D:\vcpkg\installed\x64-windows\share\plutovg\plutovgTargets-release.cmake;D:\vcpkg\installed\x64-windows\share\plutovg\plutovgTargets.cmake;D:\vcpkg\installed\x64-windows\share\png\vcpkg-cmake-wrapper.cmake;D:\vcpkg\installed\x64-windows\share\portaudio\portaudioConfig.cmake;D:\vcpkg\installed\x64-windows\share\portaudio\portaudioConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\portaudio\portaudioTargets-debug.cmake;D:\vcpkg\installed\x64-windows\share\portaudio\portaudioTargets-release.cmake;D:\vcpkg\installed\x64-windows\share\portaudio\portaudioTargets.cmake;D:\vcpkg\installed\x64-windows\share\pugixml\pugixml-config-version.cmake;D:\vcpkg\installed\x64-windows\share\pugixml\pugixml-config.cmake;D:\vcpkg\installed\x64-windows\share\pugixml\pugixml-targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\pugixml\pugixml-targets-release.cmake;D:\vcpkg\installed\x64-windows\share\pugixml\pugixml-targets.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\SDL2Config.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\SDL2ConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\SDL2Targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\SDL2Targets-release.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\SDL2Targets.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\SDL2mainTargets-debug.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\SDL2mainTargets-release.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\SDL2mainTargets.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\sdlfind.cmake;D:\vcpkg\installed\x64-windows\share\spdlog\spdlogConfig.cmake;D:\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets-debug.cmake;D:\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets-release.cmake;D:\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets.cmake;D:\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\tbb\TBBConfig.cmake;D:\vcpkg\installed\x64-windows\share\tbb\TBBConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\tbb\TBBTargets-debug.cmake;D:\vcpkg\installed\x64-windows\share\tbb\TBBTargets-release.cmake;D:\vcpkg\installed\x64-windows\share\tbb\TBBTargets.cmake;D:\vcpkg\installed\x64-windows\share\toml11\toml11Config.cmake;D:\vcpkg\installed\x64-windows\share\toml11\toml11ConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\toml11\toml11Targets.cmake;D:\vcpkg\installed\x64-windows\share\tsl-robin-map\tsl-robin-mapConfig.cmake;D:\vcpkg\installed\x64-windows\share\tsl-robin-map\tsl-robin-mapConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\tsl-robin-map\tsl-robin-mapTargets.cmake;D:\vcpkg\installed\x64-windows\share\unofficial-spirv-reflect\unofficial-spirv-reflect-config-debug.cmake;D:\vcpkg\installed\x64-windows\share\unofficial-spirv-reflect\unofficial-spirv-reflect-config-release.cmake;D:\vcpkg\installed\x64-windows\share\unofficial-spirv-reflect\unofficial-spirv-reflect-config.cmake;D:\vcpkg\installed\x64-windows\share\xbyak\xbyak-config-version.cmake;D:\vcpkg\installed\x64-windows\share\xbyak\xbyak-config.cmake;D:\vcpkg\installed\x64-windows\share\xbyak\xbyak-targets.cmake;D:\vcpkg\installed\x64-windows\share\xxhash\xxHashConfig.cmake;D:\vcpkg\installed\x64-windows\share\xxhash\xxHashConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\xxhash\xxHashTargets-debug.cmake;D:\vcpkg\installed\x64-windows\share\xxhash\xxHashTargets-release.cmake;D:\vcpkg\installed\x64-windows\share\xxhash\xxHashTargets.cmake;D:\vcpkg\installed\x64-windows\share\zlib-ng\zlib-ng-config-version.cmake;D:\vcpkg\installed\x64-windows\share\zlib-ng\zlib-ng-config.cmake;D:\vcpkg\installed\x64-windows\share\zlib-ng\zlib-ng-debug.cmake;D:\vcpkg\installed\x64-windows\share\zlib-ng\zlib-ng-release.cmake;D:\vcpkg\installed\x64-windows\share\zlib-ng\zlib-ng.cmake;D:\vcpkg\installed\x64-windows\share\zlib\vcpkg-cmake-wrapper.cmake;D:\vcpkg\installed\x64-windows\share\zycore\zyan-functions.cmake;D:\vcpkg\installed\x64-windows\share\zycore\zycore-config-version.cmake;D:\vcpkg\installed\x64-windows\share\zycore\zycore-config.cmake;D:\vcpkg\installed\x64-windows\share\zycore\zycore-targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\zycore\zycore-targets-release.cmake;D:\vcpkg\installed\x64-windows\share\zycore\zycore-targets.cmake;D:\vcpkg\installed\x64-windows\share\zydis\zydis-config-version.cmake;D:\vcpkg\installed\x64-windows\share\zydis\zydis-config.cmake;D:\vcpkg\installed\x64-windows\share\zydis\zydis-targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\zydis\zydis-targets-release.cmake;D:\vcpkg\installed\x64-windows\share\zydis\zydis-targets.cmake;D:\vcpkg\scripts\buildsystems\vcpkg.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\sss\src\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule D:/sss/src/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/sss/src -BD:/sss/src/build --check-stamp-file D:/sss/src/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeCInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeCXXInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeDependentOption.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeGenericSystem.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeLanguageInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakePushCheckState.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeRCInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CheckCSourceCompiles.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CheckIncludeFile.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CheckLibraryExists.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CheckSymbolExists.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\MSVC-C.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\MSVC.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\FeatureSummary.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\FindBZip2.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\FindOpenSSL.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\FindPNG.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\FindPackageMessage.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\FindThreads.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\FindVulkan.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\FindZLIB.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Platform\Windows-MSVC-C.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Platform\Windows.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Platform\WindowsPaths.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\SelectLibraryConfigurations.cmake;D:\bin\llvm-project-main\llvm\cmake\modules\LLVM-Config.cmake;D:\sss\src\build\CMakeFiles\3.30.5-msvc23\CMakeCCompiler.cmake;D:\sss\src\build\CMakeFiles\3.30.5-msvc23\CMakeCXXCompiler.cmake;D:\sss\src\build\CMakeFiles\3.30.5-msvc23\CMakeRCCompiler.cmake;D:\sss\src\build\CMakeFiles\3.30.5-msvc23\CMakeSystem.cmake;D:\vcpkg\installed\x64-windows\share\Tracy\TracyConfig.cmake;D:\vcpkg\installed\x64-windows\share\Tracy\TracyTargets-debug.cmake;D:\vcpkg\installed\x64-windows\share\Tracy\TracyTargets-release.cmake;D:\vcpkg\installed\x64-windows\share\Tracy\TracyTargets.cmake;D:\vcpkg\installed\x64-windows\share\capstone\capstone-config-version.cmake;D:\vcpkg\installed\x64-windows\share\capstone\capstone-config.cmake;D:\vcpkg\installed\x64-windows\share\capstone\capstone-targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\capstone\capstone-targets-release.cmake;D:\vcpkg\installed\x64-windows\share\capstone\capstone-targets.cmake;D:\vcpkg\installed\x64-windows\share\fmt\fmt-config-version.cmake;D:\vcpkg\installed\x64-windows\share\fmt\fmt-config.cmake;D:\vcpkg\installed\x64-windows\share\fmt\fmt-targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\fmt\fmt-targets-release.cmake;D:\vcpkg\installed\x64-windows\share\fmt\fmt-targets.cmake;D:\vcpkg\installed\x64-windows\share\freetype\freetype-config-version.cmake;D:\vcpkg\installed\x64-windows\share\freetype\freetype-config.cmake;D:\vcpkg\installed\x64-windows\share\freetype\freetype-targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\freetype\freetype-targets-release.cmake;D:\vcpkg\installed\x64-windows\share\freetype\freetype-targets.cmake;D:\vcpkg\installed\x64-windows\share\freetype\vcpkg-cmake-wrapper.cmake;D:\vcpkg\installed\x64-windows\share\glslang\glslang-config-version.cmake;D:\vcpkg\installed\x64-windows\share\glslang\glslang-config.cmake;D:\vcpkg\installed\x64-windows\share\glslang\glslang-targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\glslang\glslang-targets-release.cmake;D:\vcpkg\installed\x64-windows\share\glslang\glslang-targets.cmake;D:\vcpkg\installed\x64-windows\share\imgui\imgui-config.cmake;D:\vcpkg\installed\x64-windows\share\imgui\imgui-targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\imgui\imgui-targets-release.cmake;D:\vcpkg\installed\x64-windows\share\imgui\imgui-targets.cmake;D:\vcpkg\installed\x64-windows\share\libzip\libzip-config-version.cmake;D:\vcpkg\installed\x64-windows\share\libzip\libzip-config.cmake;D:\vcpkg\installed\x64-windows\share\libzip\libzip-targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\libzip\libzip-targets-release.cmake;D:\vcpkg\installed\x64-windows\share\libzip\libzip-targets.cmake;D:\vcpkg\installed\x64-windows\share\nlohmann_json\nlohmann_jsonConfig.cmake;D:\vcpkg\installed\x64-windows\share\nlohmann_json\nlohmann_jsonConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\nlohmann_json\nlohmann_jsonTargets.cmake;D:\vcpkg\installed\x64-windows\share\openssl\vcpkg-cmake-wrapper.cmake;D:\vcpkg\installed\x64-windows\share\plutosvg\plutosvgConfig.cmake;D:\vcpkg\installed\x64-windows\share\plutosvg\plutosvgConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\plutosvg\plutosvgTargets-debug.cmake;D:\vcpkg\installed\x64-windows\share\plutosvg\plutosvgTargets-release.cmake;D:\vcpkg\installed\x64-windows\share\plutosvg\plutosvgTargets.cmake;D:\vcpkg\installed\x64-windows\share\plutovg\plutovgConfig.cmake;D:\vcpkg\installed\x64-windows\share\plutovg\plutovgConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\plutovg\plutovgTargets-debug.cmake;D:\vcpkg\installed\x64-windows\share\plutovg\plutovgTargets-release.cmake;D:\vcpkg\installed\x64-windows\share\plutovg\plutovgTargets.cmake;D:\vcpkg\installed\x64-windows\share\png\vcpkg-cmake-wrapper.cmake;D:\vcpkg\installed\x64-windows\share\portaudio\portaudioConfig.cmake;D:\vcpkg\installed\x64-windows\share\portaudio\portaudioConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\portaudio\portaudioTargets-debug.cmake;D:\vcpkg\installed\x64-windows\share\portaudio\portaudioTargets-release.cmake;D:\vcpkg\installed\x64-windows\share\portaudio\portaudioTargets.cmake;D:\vcpkg\installed\x64-windows\share\pugixml\pugixml-config-version.cmake;D:\vcpkg\installed\x64-windows\share\pugixml\pugixml-config.cmake;D:\vcpkg\installed\x64-windows\share\pugixml\pugixml-targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\pugixml\pugixml-targets-release.cmake;D:\vcpkg\installed\x64-windows\share\pugixml\pugixml-targets.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\SDL2Config.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\SDL2ConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\SDL2Targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\SDL2Targets-release.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\SDL2Targets.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\SDL2mainTargets-debug.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\SDL2mainTargets-release.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\SDL2mainTargets.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\sdlfind.cmake;D:\vcpkg\installed\x64-windows\share\spdlog\spdlogConfig.cmake;D:\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets-debug.cmake;D:\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets-release.cmake;D:\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets.cmake;D:\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\tbb\TBBConfig.cmake;D:\vcpkg\installed\x64-windows\share\tbb\TBBConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\tbb\TBBTargets-debug.cmake;D:\vcpkg\installed\x64-windows\share\tbb\TBBTargets-release.cmake;D:\vcpkg\installed\x64-windows\share\tbb\TBBTargets.cmake;D:\vcpkg\installed\x64-windows\share\toml11\toml11Config.cmake;D:\vcpkg\installed\x64-windows\share\toml11\toml11ConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\toml11\toml11Targets.cmake;D:\vcpkg\installed\x64-windows\share\tsl-robin-map\tsl-robin-mapConfig.cmake;D:\vcpkg\installed\x64-windows\share\tsl-robin-map\tsl-robin-mapConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\tsl-robin-map\tsl-robin-mapTargets.cmake;D:\vcpkg\installed\x64-windows\share\unofficial-spirv-reflect\unofficial-spirv-reflect-config-debug.cmake;D:\vcpkg\installed\x64-windows\share\unofficial-spirv-reflect\unofficial-spirv-reflect-config-release.cmake;D:\vcpkg\installed\x64-windows\share\unofficial-spirv-reflect\unofficial-spirv-reflect-config.cmake;D:\vcpkg\installed\x64-windows\share\xbyak\xbyak-config-version.cmake;D:\vcpkg\installed\x64-windows\share\xbyak\xbyak-config.cmake;D:\vcpkg\installed\x64-windows\share\xbyak\xbyak-targets.cmake;D:\vcpkg\installed\x64-windows\share\xxhash\xxHashConfig.cmake;D:\vcpkg\installed\x64-windows\share\xxhash\xxHashConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\xxhash\xxHashTargets-debug.cmake;D:\vcpkg\installed\x64-windows\share\xxhash\xxHashTargets-release.cmake;D:\vcpkg\installed\x64-windows\share\xxhash\xxHashTargets.cmake;D:\vcpkg\installed\x64-windows\share\zlib-ng\zlib-ng-config-version.cmake;D:\vcpkg\installed\x64-windows\share\zlib-ng\zlib-ng-config.cmake;D:\vcpkg\installed\x64-windows\share\zlib-ng\zlib-ng-debug.cmake;D:\vcpkg\installed\x64-windows\share\zlib-ng\zlib-ng-release.cmake;D:\vcpkg\installed\x64-windows\share\zlib-ng\zlib-ng.cmake;D:\vcpkg\installed\x64-windows\share\zlib\vcpkg-cmake-wrapper.cmake;D:\vcpkg\installed\x64-windows\share\zycore\zyan-functions.cmake;D:\vcpkg\installed\x64-windows\share\zycore\zycore-config-version.cmake;D:\vcpkg\installed\x64-windows\share\zycore\zycore-config.cmake;D:\vcpkg\installed\x64-windows\share\zycore\zycore-targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\zycore\zycore-targets-release.cmake;D:\vcpkg\installed\x64-windows\share\zycore\zycore-targets.cmake;D:\vcpkg\installed\x64-windows\share\zydis\zydis-config-version.cmake;D:\vcpkg\installed\x64-windows\share\zydis\zydis-config.cmake;D:\vcpkg\installed\x64-windows\share\zydis\zydis-targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\zydis\zydis-targets-release.cmake;D:\vcpkg\installed\x64-windows\share\zydis\zydis-targets.cmake;D:\vcpkg\scripts\buildsystems\vcpkg.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\sss\src\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="D:\sss\src\build\ZERO_CHECK.vcxproj">
      <Project>{48EB4516-62B6-3237-9D7B-260E05A6716B}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\sss\src\build\PS4Emulator.vcxproj">
      <Project>{BA46E6C6-487F-3146-AF7E-C990F68BCA07}</Project>
      <Name>PS4Emulator</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>
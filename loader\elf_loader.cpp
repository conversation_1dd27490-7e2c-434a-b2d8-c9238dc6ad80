#define NOMINMAX
#include "elf_loader.h"
#include "../memory/ps4_mmu.h"
#include "../ps4/ps4_emulator.h"
#include <algorithm>
#include <chrono>
#include <cstring>
#include <fstream>
#include <spdlog/spdlog.h>
#include <stdexcept>
#include <vector>
// Placeholder for crypto (stubbed AES-256-CBC)
#include <openssl/aes.h>

namespace ps4 {

struct ElfLoadException : std::runtime_error {
  explicit ElfLoadException(const std::string &msg) : std::runtime_error(msg) {}
};

void LoadedElf::Save(std::ostream &out) const {
  try {
    uint32_t version = 1;
    out.write(reinterpret_cast<const char *>(&version), sizeof(version));
    uint64_t segCount = loadedSegments.size();
    out.write(reinterpret_cast<const char *>(&segCount), sizeof(segCount));
    for (const auto &seg : loadedSegments) {
      out.write(reinterpret_cast<const char *>(&seg), sizeof(seg));
    }
    out.write(reinterpret_cast<const char *>(&entryPoint), sizeof(entryPoint));
    out.write(reinterpret_cast<const char *>(&baseLoadAddress), sizeof(baseLoadAddress));
    out.write(reinterpret_cast<const char *>(&phdrAddress), sizeof(phdrAddress));
    out.write(reinterpret_cast<const char *>(&phdrEntrySize), sizeof(phdrEntrySize));
    out.write(reinterpret_cast<const char *>(&phdrNum), sizeof(phdrNum));
    out.write(reinterpret_cast<const char *>(&dynamicAddress), sizeof(dynamicAddress));
    out.write(reinterpret_cast<const char *>(&dynamicSize), sizeof(dynamicSize));
    out.write(reinterpret_cast<const char *>(&dynSymTableAddr), sizeof(dynSymTableAddr));
    out.write(reinterpret_cast<const char *>(&dynStrTableAddr), sizeof(dynStrTableAddr));
    out.write(reinterpret_cast<const char *>(&dynStrTableSize), sizeof(dynStrTableSize));
    out.write(reinterpret_cast<const char *>(&relaDynAddr), sizeof(relaDynAddr));
    out.write(reinterpret_cast<const char *>(&relaDynSize), sizeof(relaDynSize));
    out.write(reinterpret_cast<const char *>(&relaPltAddr), sizeof(relaPltAddr));
    out.write(reinterpret_cast<const char *>(&relaPltSize), sizeof(relaPltSize));
    out.write(reinterpret_cast<const char *>(&pltGotAddr), sizeof(pltGotAddr));
    out.write(reinterpret_cast<const char *>(&metadataAddr), sizeof(metadataAddr));
    out.write(reinterpret_cast<const char *>(&metadataSize), sizeof(metadataSize));
    out.write(reinterpret_cast<const char *>(&isSelf), sizeof(isSelf));
    uint64_t selfSegCount = selfSegments.size();
    out.write(reinterpret_cast<const char *>(&selfSegCount), sizeof(selfSegCount));
    for (const auto &seg : selfSegments) {
      out.write(reinterpret_cast<const char *>(&seg), sizeof(seg));
    }
    uint64_t symCount = dynSymbols.size();
    out.write(reinterpret_cast<const char *>(&symCount), sizeof(symCount));
    for (const auto &sym : dynSymbols) {
      sym.Save(out);
    }
    uint64_t strSize = dynStringTable.size();
    out.write(reinterpret_cast<const char *>(&strSize), sizeof(strSize));
    out.write(dynStringTable.data(), strSize);
    uint64_t resSymCount = resolvedSymbols.size();
    out.write(reinterpret_cast<const char *>(&resSymCount), sizeof(resSymCount));
    for (const auto &[name, addr] : resolvedSymbols) {
      uint32_t nameLen = static_cast<uint32_t>(name.size());
      out.write(reinterpret_cast<const char *>(&nameLen), sizeof(nameLen));
      out.write(name.data(), nameLen);
      out.write(reinterpret_cast<const char *>(&addr), sizeof(addr));
    }
    if (!out.good()) throw ElfLoadException("Failed to write ELF metadata");
    spdlog::info("LoadedElf state saved: {} segments, {} symbols", segCount, symCount);
  } catch (const std::exception &e) {
    spdlog::error("Failed to save LoadedElf state: {}", e.what());
    throw ElfLoadException("Save state failure");
  }
}

void LoadedElf::Load(std::istream &in) {
  try {
    uint32_t version;
    in.read(reinterpret_cast<char *>(&version), sizeof(version));
    if (version != 1) throw ElfLoadException("Invalid LoadedElf state version");
    uint64_t segCount;
    in.read(reinterpret_cast<char *>(&segCount), sizeof(segCount));
    loadedSegments.resize(segCount);
    for (auto &seg : loadedSegments) {
      in.read(reinterpret_cast<char *>(&seg), sizeof(seg));
    }
    in.read(reinterpret_cast<char *>(&entryPoint), sizeof(entryPoint));
    in.read(reinterpret_cast<char *>(&baseLoadAddress), sizeof(baseLoadAddress));
    in.read(reinterpret_cast<char *>(&phdrAddress), sizeof(phdrAddress));
    in.read(reinterpret_cast<char *>(&phdrEntrySize), sizeof(phdrEntrySize));
    in.read(reinterpret_cast<char *>(&phdrNum), sizeof(phdrNum));
    in.read(reinterpret_cast<char *>(&dynamicAddress), sizeof(dynamicAddress));
    in.read(reinterpret_cast<char *>(&dynamicSize), sizeof(dynamicSize));
    in.read(reinterpret_cast<char *>(&dynSymTableAddr), sizeof(dynSymTableAddr));
    in.read(reinterpret_cast<char *>(&dynStrTableAddr), sizeof(dynStrTableAddr));
    in.read(reinterpret_cast<char *>(&dynStrTableSize), sizeof(dynStrTableSize));
    in.read(reinterpret_cast<char *>(&relaDynAddr), sizeof(relaDynAddr));
    in.read(reinterpret_cast<char *>(&relaDynSize), sizeof(relaDynSize));
    in.read(reinterpret_cast<char *>(&relaPltAddr), sizeof(relaPltAddr));
    in.read(reinterpret_cast<char *>(&relaPltSize), sizeof(relaPltSize));
    in.read(reinterpret_cast<char *>(&pltGotAddr), sizeof(pltGotAddr));
    in.read(reinterpret_cast<char *>(&metadataAddr), sizeof(metadataAddr));
    in.read(reinterpret_cast<char *>(&metadataSize), sizeof(metadataSize));
    in.read(reinterpret_cast<char *>(&isSelf), sizeof(isSelf));
    uint64_t selfSegCount;
    in.read(reinterpret_cast<char *>(&selfSegCount), sizeof(selfSegCount));
    selfSegments.resize(selfSegCount);
    for (auto &seg : selfSegments) {
      in.read(reinterpret_cast<char *>(&seg), sizeof(seg));
    }
    uint64_t symCount;
    in.read(reinterpret_cast<char *>(&symCount), sizeof(symCount));
    dynSymbols.resize(symCount);
    for (auto &sym : dynSymbols) {
      sym.Load(in);
    }
    uint64_t strSize;
    in.read(reinterpret_cast<char *>(&strSize), sizeof(strSize));
    dynStringTable.resize(strSize);
    in.read(dynStringTable.data(), strSize);
    uint64_t resSymCount;
    in.read(reinterpret_cast<char *>(&resSymCount), sizeof(resSymCount));
    resolvedSymbols.clear();
    for (uint64_t i = 0; i < resSymCount; ++i) {
      uint32_t nameLen;
      in.read(reinterpret_cast<char *>(&nameLen), sizeof(nameLen));
      std::string name(nameLen, '\0');
      in.read(name.data(), nameLen);
      uint64_t addr;
      in.read(reinterpret_cast<char *>(&addr), sizeof(addr));
      resolvedSymbols[name] = addr;
    }
    if (!in.good()) throw ElfLoadException("Failed to read ELF metadata");
    spdlog::info("LoadedElf state loaded: {} segments, {} symbols", segCount, symCount);
  } catch (const std::exception &e) {
    spdlog::error("Failed to load LoadedElf state: {}", e.what());
    throw ElfLoadException("Load state failure");
  }
}

ElfLoader::ElfLoader(PS4Emulator &emu) : m_emulator(emu), m_currentProcessId(1) {
  spdlog::info("ElfLoader initialized");
}

ElfLoader::~ElfLoader() noexcept {
  std::lock_guard<std::mutex> lock(m_mutex);
  m_stats.clear();
  spdlog::info("ElfLoader destroyed");
}

bool ElfLoader::Load(const std::string &filename, LoadedElf &loadedElf,
                     bool isSharedObject, uint64_t processId) {
  auto start = std::chrono::steady_clock::now();
  std::lock_guard<std::mutex> lock(m_mutex);
  m_currentProcessId = processId;
  spdlog::info("Loading ELF/SELF: {}, process ID: {}, shared: {}", filename, processId, isSharedObject);

  try {
    std::string resolvedPath = m_emulator.GetFilesystem().MapToHostPath(filename);
    if (!std::filesystem::exists(resolvedPath)) {
      spdlog::error("File does not exist: {}", resolvedPath);
      auto parent = std::filesystem::path(resolvedPath).parent_path();
      if (!std::filesystem::exists(parent)) {
        std::filesystem::create_directories(parent);
        spdlog::warn("Created parent directory: {}", parent.string());
      }
      throw ElfLoadException("File does not exist");
    }

    std::ifstream file(resolvedPath, std::ios::binary);
    if (!file.is_open()) {
      spdlog::error("Failed to open file: {}", resolvedPath);
      throw ElfLoadException("File open failure");
    }

    elf::self_header selfHdr;
    file.read(reinterpret_cast<char *>(&selfHdr), sizeof(selfHdr));
    file.seekg(0);
    loadedElf.isSelf = selfHdr.isValid();
    uint64_t elfOffset = 0;

    if (loadedElf.isSelf) {
      spdlog::info("Detected SELF file: {}", filename);
      loadedElf.selfSegments.resize(selfHdr.segment_count);
      file.seekg(sizeof(selfHdr));
      file.read(reinterpret_cast<char *>(loadedElf.selfSegments.data()),
                selfHdr.segment_count * sizeof(elf::self_segment_header));
      elfOffset = sizeof(selfHdr) + selfHdr.segment_count * sizeof(elf::self_segment_header) + selfHdr.meta_size;
      file.seekg(elfOffset);
    }

    elf::Elf64_Ehdr elfHeader{};
    file.read(reinterpret_cast<char *>(&elfHeader), sizeof(elfHeader));
    if (!file.good() || !elfHeader.isValidElf64()) {
      spdlog::error("Invalid ELF64 header: {}", filename);
      throw ElfLoadException("Invalid ELF header");
    }

    if (elfHeader.e_type != elf::ET_EXEC && elfHeader.e_type != elf::ET_DYN &&
        elfHeader.e_type != elf::ET_SCE_DYNEXEC && elfHeader.e_type != elf::ET_SCE_DYNAMIC) {
      spdlog::error("Unsupported ELF type: {}", elfHeader.e_type);
      throw ElfLoadException("Unsupported ELF type");
    }

    if (!LoadSegments(file, elfHeader, loadedElf)) {
      return false;
    }
    if (loadedElf.dynamicAddress != 0 && !ParseDynamicSection(loadedElf)) {
      return false;
    }
    if (loadedElf.dynSymTableAddr != 0 && !ReadDynamicTables(loadedElf)) {
      return false;
    }
    if (loadedElf.relaDynAddr != 0 &&
        !ProcessRelocations(loadedElf, loadedElf.relaDynAddr, loadedElf.relaDynSize, false)) {
      return false;
    }
    if (loadedElf.relaPltAddr != 0 &&
        !ProcessRelocations(loadedElf, loadedElf.relaPltAddr, loadedElf.relaPltSize, true)) {
      return false;
    }

    loadedElf.entryPoint = elfHeader.e_entry +
                           (elfHeader.e_type == elf::ET_DYN || elfHeader.e_type == elf::ET_SCE_DYNAMIC
                               ? loadedElf.baseLoadAddress : 0);

    m_emulator.SetCPUEntryPoints(loadedElf.entryPoint);
    m_emulator.AddLoadedModule(filename, loadedElf.entryPoint);
    m_stats[filename + "_load_count"]++;
    m_stats[filename + "_relocations"] +=
        loadedElf.relaDynSize / sizeof(elf::Elf64_Rela) + loadedElf.relaPltSize / sizeof(elf::Elf64_Rela);
    m_stats[filename + "_symbols_resolved"] += loadedElf.resolvedSymbols.size();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(
                        std::chrono::steady_clock::now() - start).count();
    m_stats[filename + "_load_time_ms"] = duration;
    spdlog::info("ELF/SELF '{}' loaded: entry=0x{:x}, pid={}, time={}ms, relocations={}, symbols={}",
                 filename, loadedElf.entryPoint, processId, duration,
                 m_stats[filename + "_relocations"], m_stats[filename + "_symbols_resolved"]);
    return true;
  } catch (const ElfLoadException &e) {
    spdlog::error("ELF/SELF load failed for '{}': {}", filename, e.what());
    return false;
  } catch (const std::exception &e) {
    spdlog::error("Unexpected error loading ELF/SELF '{}': {}", filename, e.what());
    return false;
  }
}

bool ElfLoader::LoadSegments(std::ifstream &file, const elf::Elf64_Ehdr &elfHeader,
                             LoadedElf &loadedElf) {
  std::lock_guard<std::mutex> lock(m_mutex);
  try {
    uint64_t minVaddr = UINT64_MAX, maxVaddrEnd = 0;
    std::vector<elf::Elf64_Phdr> headers(elfHeader.e_phnum);

    file.seekg(elfHeader.e_phoff);
    file.read(reinterpret_cast<char *>(headers.data()), headers.size() * sizeof(elf::Elf64_Phdr));
    if (!file.good()) throw ElfLoadException("Program header read failure");

    for (const auto &phdr : headers) {
      if (!phdr.isValid()) {
        spdlog::error("Invalid program header: type={}, align={}", phdr.p_type, phdr.p_align);
        throw ElfLoadException("Invalid program header");
      }
      if (phdr.p_type == elf::PT_LOAD) {
        minVaddr = std::min(minVaddr, phdr.p_vaddr);
        maxVaddrEnd = std::max(maxVaddrEnd, phdr.p_vaddr + phdr.p_memsz);
      }
    }

    if (elfHeader.e_type == elf::ET_DYN || elfHeader.e_type == elf::ET_SCE_DYNAMIC) {
      loadedElf.baseLoadAddress = m_emulator.GetOrbisOS().AllocateVirtualMemory(
          maxVaddrEnd - minVaddr, 4096, false);
      if (!loadedElf.baseLoadAddress) throw ElfLoadException("Base address allocation failure");
      m_stats["memory_allocations"]++;
    }

    for (const auto &phdr : headers) {
      if (phdr.p_type == elf::PT_LOAD) {
        uint64_t vaddr = phdr.p_vaddr +
                         (elfHeader.e_type == elf::ET_DYN || elfHeader.e_type == elf::ET_SCE_DYNAMIC
                             ? loadedElf.baseLoadAddress : 0);
        uint64_t addr = m_emulator.GetOrbisOS().AllocateVirtualMemory(phdr.p_memsz, 4096, false);
        if (!addr) throw ElfLoadException("Segment allocation failure");
        int prot = (phdr.p_flags & elf::PF_R ? ps4::PROT_READ : 0) |
                   (phdr.p_flags & elf::PF_W ? ps4::PROT_WRITE : 0) |
                   (phdr.p_flags & elf::PF_X ? ps4::PROT_EXEC : 0);
        m_emulator.GetOrbisOS().ProtectMemory(addr, phdr.p_memsz, prot);

        uint64_t fileOffset = phdr.p_offset;
        if (loadedElf.isSelf) {
          for (const auto &seg : loadedElf.selfSegments) {
            if (seg.IsBlocked() && fileOffset >= phdr.p_offset &&
                fileOffset < phdr.p_offset + phdr.p_filesz) {
              fileOffset = (fileOffset - phdr.p_offset) + seg.file_offset;
              break;
            }
          }
        }

        file.seekg(fileOffset);
        std::vector<uint8_t> data(phdr.p_filesz);
        file.read(reinterpret_cast<char *>(data.data()), phdr.p_filesz);
        if (!file.good()) throw ElfLoadException("Segment data read failure");

        if (loadedElf.isSelf) {
          for (const auto &seg : loadedElf.selfSegments) {
            if (seg.IsEncrypted() && fileOffset >= seg.file_offset &&
                fileOffset < seg.file_offset + seg.file_size) {
              // Placeholder AES-256-CBC decryption
              std::vector<uint8_t> key(32, 0); // Stubbed key
              std::vector<uint8_t> iv(16, 0);  // Stubbed IV
              AES_KEY aesKey;
              AES_set_decrypt_key(key.data(), 256, &aesKey);
              std::vector<uint8_t> decrypted(data.size());
              AES_cbc_encrypt(data.data(), decrypted.data(), data.size(), &aesKey, iv.data(), AES_DECRYPT);
              data = std::move(decrypted);
              break;
            }
          }
        }

        m_emulator.GetMMU().WriteVirtual(addr, data.data(), phdr.p_filesz, m_currentProcessId);
        m_emulator.GetMMU().MapVirtualToPhysical(vaddr, addr, phdr.p_memsz, m_currentProcessId);
        loadedElf.loadedSegments.push_back({addr, phdr.p_memsz, prot});
        m_stats["memory_allocations"]++;
        spdlog::debug("Loaded segment: addr=0x{:x}, size=0x{:x}, prot=0x{:x}", addr, phdr.p_memsz, prot);
      } else if (phdr.p_type == elf::PT_DYNAMIC) {
        loadedElf.dynamicAddress = phdr.p_vaddr +
                                   (elfHeader.e_type == elf::ET_DYN || elfHeader.e_type == elf::ET_SCE_DYNAMIC
                                       ? loadedElf.baseLoadAddress : 0);
        loadedElf.dynamicSize = phdr.p_memsz;
      } else if (phdr.p_type == elf::PT_PHDR) {
        loadedElf.phdrAddress = phdr.p_vaddr +
                                (elfHeader.e_type == elf::ET_DYN || elfHeader.e_type == elf::ET_SCE_DYNAMIC
                                    ? loadedElf.baseLoadAddress : 0);
        loadedElf.phdrEntrySize = elfHeader.e_phentsize;
        loadedElf.phdrNum = elfHeader.e_phnum;
      } else if (phdr.p_type == elf::PT_SCE_DYNLIBDATA) {
        spdlog::debug("Found SCE_DYNLIBDATA: addr=0x{:x}, size=0x{:x}", phdr.p_vaddr, phdr.p_memsz);
      }
    }

    if (elfHeader.e_shoff != 0 && elfHeader.e_shnum > 0) {
      std::vector<elf::Elf64_Shdr> sections(elfHeader.e_shnum);
      file.seekg(elfHeader.e_shoff);
      file.read(reinterpret_cast<char *>(sections.data()), sections.size() * sizeof(elf::Elf64_Shdr));
      if (!file.good()) throw ElfLoadException("Section header read failure");
      for (const auto &shdr : sections) {
        if (shdr.sh_type == elf::SHT_PS4_METADATA) {
          loadedElf.metadataAddr = shdr.sh_addr +
                                   (elfHeader.e_type == elf::ET_DYN || elfHeader.e_type == elf::ET_SCE_DYNAMIC
                                       ? loadedElf.baseLoadAddress : 0);
          loadedElf.metadataSize = shdr.sh_size;
          spdlog::debug("Found PS4 metadata section: addr=0x{:x}, size=0x{:x}",
                        loadedElf.metadataAddr, loadedElf.metadataSize);
          m_stats["metadata_sections"]++;
        }
      }
    }

    return true;
  } catch (const ElfLoadException &e) {
    spdlog::error("Segment load failed: {}", e.what());
    return false;
  } catch (const std::exception &e) {
    spdlog::error("Unexpected error in segment load: {}", e.what());
    return false;
  }
}

bool ElfLoader::ParseDynamicSection(LoadedElf &loadedElf) {
  std::lock_guard<std::mutex> lock(m_mutex);
  try {
    size_t numEntries = loadedElf.dynamicSize / sizeof(elf::Elf64_Dyn);
    std::vector<elf::Elf64_Dyn> entries(numEntries);
    m_emulator.GetMMU().ReadVirtual(loadedElf.dynamicAddress, entries.data(),
                                    loadedElf.dynamicSize, m_currentProcessId);

    for (const auto &entry : entries) {
      uint64_t value = entry.d_un.d_ptr;
      bool isPtr = false;
      switch (entry.d_tag) {
        case elf::DT_NULL: return true;
        case elf::DT_PLTGOT:
        case elf::DT_STRTAB:
        case elf::DT_SYMTAB:
        case elf::DT_RELA:
        case elf::DT_JMPREL:
        case elf::DT_SCE_MODULE_INFO:
        case elf::DT_SCE_NEEDED_MODULE:
        case elf::DT_SCE_EXPORT_LIB:
        case elf::DT_SCE_IMPORT_LIB:
          isPtr = true;
          break;
      }
      if (isPtr && loadedElf.baseLoadAddress != 0 && value != 0) {
        value += loadedElf.baseLoadAddress;
      }
      switch (entry.d_tag) {
        case elf::DT_NEEDED: break;
        case elf::DT_PLTRELSZ: loadedElf.relaPltSize = entry.d_un.d_val; break;
        case elf::DT_PLTGOT: loadedElf.pltGotAddr = value; break;
        case elf::DT_STRTAB: loadedElf.dynStrTableAddr = value; break;
        case elf::DT_SYMTAB: loadedElf.dynSymTableAddr = value; break;
        case elf::DT_RELA: loadedElf.relaDynAddr = value; break;
        case elf::DT_RELASZ: loadedElf.relaDynSize = entry.d_un.d_val; break;
        case elf::DT_RELAENT:
          if (entry.d_un.d_val != sizeof(elf::Elf64_Rela))
            throw ElfLoadException("Invalid RELA entry size");
          break;
        case elf::DT_STRSZ: loadedElf.dynStrTableSize = entry.d_un.d_val; break;
        case elf::DT_SYMENT:
          if (entry.d_un.d_val != sizeof(elf::Elf64_Sym))
            throw ElfLoadException("Invalid symbol entry size");
          break;
        case elf::DT_PLTREL:
          if (entry.d_un.d_val != elf::DT_RELA)
            throw ElfLoadException("Invalid PLT relocation type");
          break;
        case elf::DT_JMPREL: loadedElf.relaPltAddr = value; break;
        case elf::DT_SCE_MODULE_INFO:
        case elf::DT_SCE_NEEDED_MODULE:
        case elf::DT_SCE_EXPORT_LIB:
        case elf::DT_SCE_IMPORT_LIB:
          spdlog::debug("Processed PS4 dynamic tag: {}", entry.d_tag);
          break;
        default: spdlog::debug("Ignored dynamic tag: {}", entry.d_tag); break;
      }
    }
    spdlog::trace("Parsed dynamic section: {} entries", numEntries);
    return true;
  } catch (const ElfLoadException &e) {
    spdlog::error("Dynamic section parsing failed: {}", e.what());
    return false;
  } catch (const std::exception &e) {
    spdlog::error("Unexpected error in dynamic section parsing: {}", e.what());
    return false;
  }
}

bool ElfLoader::ReadDynamicTables(LoadedElf &loadedElf) {
  std::lock_guard<std::mutex> lock(m_mutex);
  try {
    if (loadedElf.dynSymTableAddr == 0 || loadedElf.dynStrTableAddr == 0 ||
        loadedElf.dynStrTableSize == 0) {
      spdlog::warn("No dynamic symbol or string table present");
      return false;
    }
    uint64_t symTableSize = (loadedElf.dynStrTableAddr > loadedElf.dynSymTableAddr)
                                ? (loadedElf.dynStrTableAddr - loadedElf.dynSymTableAddr)
                                : (4096 * sizeof(elf::Elf64_Sym));
    if (symTableSize == 0 || symTableSize % sizeof(elf::Elf64_Sym) != 0) {
      spdlog::error("Invalid symbol table size: {}", symTableSize);
      return false;
    }
    size_t numSymbols = symTableSize / sizeof(elf::Elf64_Sym);
    loadedElf.dynSymbols.resize(numSymbols);
    loadedElf.dynStringTable.resize(loadedElf.dynStrTableSize);
    m_emulator.GetMMU().ReadVirtual(loadedElf.dynSymTableAddr, loadedElf.dynSymbols.data(),
                                    symTableSize, m_currentProcessId);
    m_emulator.GetMMU().ReadVirtual(loadedElf.dynStrTableAddr, loadedElf.dynStringTable.data(),
                                    loadedElf.dynStrTableSize, m_currentProcessId);
    spdlog::info("Read dynamic tables: symbols={}, strings={} bytes", numSymbols, loadedElf.dynStrTableSize);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("Failed to read dynamic tables: {}", e.what());
    return false;
  }
}

bool ElfLoader::ProcessRelocations(LoadedElf &loadedElf, uint64_t relaAddr, uint64_t relaSize, bool isPlt) {
  std::lock_guard<std::mutex> lock(m_mutex);
  try {
    if (relaAddr == 0 || relaSize == 0) {
      spdlog::trace("No relocations to process (isPlt={})", isPlt);
      return true;
    }
    size_t entrySize = sizeof(elf::Elf64_Rela);
    if (relaSize % entrySize != 0) throw ElfLoadException("Invalid relocation table size");
    size_t numRelocs = relaSize / entrySize;
    std::vector<elf::Elf64_Rela> relocs(numRelocs);
    m_emulator.GetMMU().ReadVirtual(relaAddr, relocs.data(), relaSize, m_currentProcessId);

    auto start = std::chrono::steady_clock::now();
    for (const auto &rela : relocs) {
      uint64_t offset = rela.r_offset + loadedElf.baseLoadAddress;
      uint64_t symIndex = ELF64_R_SYM(rela.r_info);
      uint32_t relaType = ELF64_R_TYPE(rela.r_info);
      uint64_t S = 0;
      uint64_t A = rela.r_addend;
      uint64_t B = loadedElf.baseLoadAddress;
      uint64_t P = offset;

      if (symIndex < loadedElf.dynSymbols.size() &&
          loadedElf.dynSymbols[symIndex].st_name < loadedElf.dynStringTable.size()) {
        std::string symName(&loadedElf.dynStringTable[loadedElf.dynSymbols[symIndex].st_name]);
        S = ResolveSymbol(symName, loadedElf);
      }

      uint64_t value = 0;
      try {
        switch (relaType) {
          case elf::R_X86_64_RELATIVE:
          case elf::R_X86_64_PS4_RELATIVE:
            value = B + A;
            m_emulator.GetMMU().WriteVirtual(offset, reinterpret_cast<uint8_t *>(&value),
                                            sizeof(value), m_currentProcessId);
            break;
          case elf::R_X86_64_64:
          case elf::R_X86_64_PS4_64:
            value = S + A;
            m_emulator.GetMMU().WriteVirtual(offset, reinterpret_cast<uint8_t *>(&value),
                                            sizeof(value), m_currentProcessId);
            break;
          case elf::R_X86_64_GLOB_DAT:
          case elf::R_X86_64_JUMP_SLOT:
          case elf::R_X86_64_PS4_GLOB_DAT:
            value = S;
            m_emulator.GetMMU().WriteVirtual(offset, reinterpret_cast<uint8_t *>(&value),
                                            sizeof(value), m_currentProcessId);
            break;
          case elf::R_X86_64_PC32:
            {
              value = S + A - P;
              uint32_t v32 = static_cast<uint32_t>(value);
              m_emulator.GetMMU().WriteVirtual(offset, reinterpret_cast<uint8_t *>(&v32),
                                              sizeof(v32), m_currentProcessId);
              break;
            }
          case elf::R_X86_64_COPY:
            if (S == 0) throw ElfLoadException("Undefined symbol for COPY relocation");
            m_emulator.GetMMU().WriteVirtual(offset, reinterpret_cast<uint8_t *>(&S),
                                            sizeof(S), m_currentProcessId);
            break;
          default:
            spdlog::warn("Unsupported relocation type {} at offset 0x{:x}", relaType, offset);
            continue;
        }
        spdlog::trace("Applied relocation type={} at offset=0x{:x}, value=0x{:x}", relaType, offset, value);
      } catch (const std::exception &e) {
        spdlog::error("Relocation failed at 0x{:x}: {}", offset, e.what());
        throw ElfLoadException("Relocation application failure");
      }
    }
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(
                        std::chrono::steady_clock::now() - start).count();
    m_stats[isPlt ? "plt_relocations" : "dyn_relocations"] += numRelocs;
    m_stats[isPlt ? "plt_relocation_time_us" : "dyn_relocation_time_us"] += duration;
    spdlog::debug("Processed {} relocations (isPlt={}) in {}us", numRelocs, isPlt, duration);
    return true;
  } catch (const ElfLoadException &e) {
    spdlog::error("Relocation processing failed (isPlt={}): {}", isPlt, e.what());
    return false;
  } catch (const std::exception &e) {
    spdlog::error("Unexpected error in relocation processing (isPlt={}): {}", isPlt, e.what());
    return false;
  }
}

uint64_t ElfLoader::ResolveSymbol(const std::string &name, LoadedElf &currentElf) {
  std::lock_guard<std::mutex> lock(m_mutex);
  try {
    auto it = currentElf.resolvedSymbols.find(name);
    if (it != currentElf.resolvedSymbols.end()) {
      m_stats["symbol_cache_hits"]++;
      return it->second;
    }
    for (const auto &sym : currentElf.dynSymbols) {
      if (sym.st_name < currentElf.dynStringTable.size()) {
        std::string symName(&currentElf.dynStringTable[sym.st_name]);
        if (symName == name && sym.st_value != 0) {
          uint64_t addr = sym.st_value + currentElf.baseLoadAddress;
          currentElf.resolvedSymbols[name] = addr;
          m_stats["symbol_resolutions"]++;
          spdlog::trace("Resolved symbol '{}' to 0x{:x}", name, addr);
          return addr;
        }
      }
    }
    spdlog::warn("Unresolved symbol: '{}'", name);
    m_stats["symbol_unresolved"]++;
    return 0;
  } catch (const std::exception &e) {
    spdlog::error("Symbol resolution failed for '{}': {}", name, e.what());
    return 0;
  }
}

} // namespace ps4